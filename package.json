{"name": "frontihm", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^16.2.12", "@angular/cdk": "^19.2.10", "@angular/common": "^16.1.0", "@angular/compiler": "^16.1.0", "@angular/core": "^16.1.0", "@angular/forms": "^16.1.0", "@angular/material": "^19.2.10", "@angular/platform-browser": "^16.1.0", "@angular/platform-browser-dynamic": "^16.1.0", "@angular/router": "^16.1.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.6", "chart.js": "^4.4.9", "ng2-charts": "^4.1.1", "primeicons": "^7.0.0", "primeng": "^19.1.2", "rxjs": "~7.8.0", "sweetalert2": "^11.19.1", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.1.0", "@angular/cli": "~16.1.0", "@angular/compiler-cli": "^16.1.0", "@types/bootstrap": "^5.2.10", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "tailwindcss": "^3.4.17", "typescript": "~5.1.3"}}