.about-container {
    font-family: 'Arial', sans-serif;
    color: #333;
    max-width: 100%;
    overflow-x: hidden;
}

.hero {
    position: relative;
    text-align: center;
    color: white;
    height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.hero img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 0;
}

.hero h1, .hero p {
    position: relative;
    z-index: 1;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero p {
    font-size: 1.5rem;
    margin-bottom: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.mission {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    gap: 3rem;
}

.mission-content {
    flex: 1;
    padding: 0 1rem;
}

.mission h2 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
    position: relative;
}

.mission h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background-color: #007bff;
}

.mission p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
}

.mission img {
    flex: 1;
    max-width: 500px;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.mission img:hover {
    transform: scale(1.02);
}

.cta {
    text-align: center;
    padding: 4rem 2rem;
    background-color: #f8f9fa;
}

.cta h2 {
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.cta-button {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.cta-button:hover {
    background-color: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

@media (max-width: 992px) {
    .hero h1 {
        font-size: 2.8rem;
    }
    
    .hero p {
        font-size: 1.2rem;
    }
    
    .mission {
        padding: 3rem 2rem;
    }
}

@media (max-width: 768px) {
    .hero {
        height: 50vh;
    }
    
    .hero h1 {
        font-size: 2.2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .mission {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }
    
    .mission h2::after {
        left: 50%;
        transform: translateX(-50%);
    }
    
    .mission img {
        max-width: 100%;
        order: -1;
    }
    
    .cta h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .hero h1 {
        font-size: 1.8rem;
    }
    
    .mission {
        padding: 2rem 1rem;
    }
    
    .mission h2 {
        font-size: 1.8rem;
    }
    
    .cta {
        padding: 3rem 1rem;
    }
}