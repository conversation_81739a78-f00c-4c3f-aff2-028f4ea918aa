import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import {MatIconModule} from '@angular/material/icon';


import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HomeComponent } from './home/<USER>';
import { LoginComponent } from './login/login.component';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { HotelComponent } from './hotel/hotel.component';
import { RoomComponent } from './room/room.component';
import { ReservationFormComponent } from './reservation-form/reservation-form.component';
import { FacilitiesComponent } from './facilities/facilities.component';
import { RouterModule } from '@angular/router';
import { AuthInterceptor } from './auth.interceptor';
import { MesReservationComponent } from './mes-reservation/mes-reservation.component';
import { ConfirmCommentaireComponent } from './confirm-commentaire/confirm-commentaire.component';
import { HotelListAdminComponent } from './hotel-list-admin/hotel-list-admin.component';
import { UserListAdminComponent } from './user-list-admin/user-list-admin.component';
import { ClientReservationComponent } from './client-reservation/client-reservation.component';
import { HeaderClientComponent } from './header-client/header-client.component';
import { AddOffreComponent } from './add-offre/add-offre.component';
import { RoomListAdminComponent } from './room-list-admin/room-list-admin.component';
import { ChambresListAdminComponent } from './chambres-list-admin/chambres-list-admin.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { NgChartsModule } from 'ng2-charts';
import { ChartModule } from 'primeng/chart';
import { AboutComponent } from './about/about.component';
import { SidebarComponent } from './sidebar/sidebar.component';

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    LoginComponent,
    HeaderComponent,
    FooterComponent,
    HotelComponent,
    RoomComponent,
    ReservationFormComponent,
    FacilitiesComponent,
    MesReservationComponent,
    ConfirmCommentaireComponent,
    HotelListAdminComponent,
    UserListAdminComponent,
    ClientReservationComponent,
    HeaderClientComponent,
    AddOffreComponent,
    RoomListAdminComponent,
    ChambresListAdminComponent,
    DashboardComponent,
    AboutComponent,
    SidebarComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    HttpClientModule,
    RouterModule,
    NgChartsModule,
    ChartModule
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
