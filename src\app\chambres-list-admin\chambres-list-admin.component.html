<div class="container mt-3">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="mb-0">
      <i class="fas fa-hotel me-2"></i>Chambres de l'hôtel "{{ hotelNom }}"
    </h2>
    <!-- <button (click)="openModalForAdd()" class="btn btn-primary"> -->
    <!-- </button> -->
  </div>
      <button class="btn btn-primary" (click)="openModalForAdd()" >

  <i class="fas fa-plus-circle" ></i> 
    </button>

  <div *ngIf="chambres.length > 0; else noChambres">
    <div class="row row-cols-1 row-cols-md-3 g-4">
      <div class="col" *ngFor="let chambre of chambres">
        <div class="card h-100 shadow-sm border-0">
          <div class="position-relative">
            <img [src]="chambre.image_chambre || 'assets/images/default-room.jpg'" 
                 class="card-img-top room-image" 
                 alt="Image de la chambre"
                 onerror="this.src='assets/images/default-room.jpg'">
            <div class="room-badge">
              <span class="badge bg-{{ chambre.disponibilite ? 'success' : 'danger' }}">
                {{ chambre.disponibilite=="disponible" ? 'Disponible' : 'Indisponible' }}
              </span>
            </div>
          </div>
          
          <div class="card-body">
            <h5 class="card-title text-primary">
              <i class="fas fa-door-open me-2"></i>Chambre {{ chambre.numero }}
            </h5>
            <p class="text-muted mb-2">
              <i class="fas fa-tag me-2"></i>{{ chambre.type_chambre }}
            </p>
            
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div>
                <span class="text-decoration-line-through text-muted me-2">
                  {{ chambre.prixparnuit | currency:'EUR' }}
                </span>
                <span class="fw-bold text-success">
                  {{ chambre.prix_avec_remise | currency:'EUR' }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="card-footer bg-transparent border-top-0">
            <div class="d-flex justify-content-end">
              <button (click)="editRoom(chambre)" class="btn btn-sm btn-outline-primary me-2" title="Modifier">
                <i class="fas fa-edit"></i>
              </button>
              <button (click)="deleteRoom(chambre.id)" class="btn btn-sm btn-outline-danger" title="Supprimer">
                <i class="fas fa-trash-alt"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template #noChambres>
    <div class="alert alert-info mt-4 text-center py-4">
      <i class="fas fa-bed fa-2x mb-3"></i>
      <h4>Aucune chambre trouvée pour cet hôtel</h4>
      <button (click)="openModalForAdd()" class="btn btn-primary mt-2">
        <i class="fas fa-plus-circle me-2"></i> Ajouter votre première chambre
      </button>
    </div>
  </ng-template>
</div>

<!-- Modal pour ajouter/modifier une chambre -->
<div class="modal fade" id="roomModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title">
          <i class="fas me-2" [class.fa-plus-circle]="!editMode" [class.fa-edit]="editMode"></i>
          {{ editMode ? 'Modifier la chambre' : 'Ajouter une chambre' }}
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label class="form-label">Image URL</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-image"></i></span>
              <input [(ngModel)]="newRoom.image_chambre" class="form-control" 
                     placeholder="URL de l'image de la chambre" name="image">
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Numéro de chambre</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
              <input [(ngModel)]="newRoom.numero" class="form-control" 
                     placeholder="Numéro de la chambre" name="numero">
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label">Type de chambre</label>
            <div class="input-group">
              <span class="input-group-text"><i class="fas fa-tag"></i></span>
              <select [(ngModel)]="newRoom.type_chambre" class="form-select" name="type">
                <option *ngFor="let type of chambreTypes" [value]="type">{{ type }}</option>
              </select>
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6 mb-3">
              <label class="form-label">Prix par nuit (€)</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-euro-sign"></i></span>
                <input type="number" [(ngModel)]="newRoom.prixparnuit" 
                       class="form-control" placeholder="Prix" name="prix">
              </div>
            </div>
            
            <!-- <div class="col-md-6 mb-3">
              <label class="form-label">Disponibilité</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
                <select [(ngModel)]="newRoom.disponibilite" class="form-select" name="dispo">
                  <option [value]="true">Disponible</option>
                  <option [value]="false">Réservée</option>
                </select>
              </div>
            </div> -->
          </div>
        </form>
      </div>
      
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-2"></i>Annuler
        </button>
        <button type="button" class="btn btn-primary" (click)="saveRoom()">
          <i class="fas me-2" [class.fa-save]="editMode" [class.fa-plus-circle]="!editMode"></i>
          {{ editMode ? 'Enregistrer' : 'Ajouter' }}
        </button>
      </div>
    </div>
  </div>
</div>
