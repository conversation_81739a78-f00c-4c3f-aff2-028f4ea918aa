.commentaires-container {
    max-width: 1500px;
    margin: 30px auto;
    padding: 25px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  }


  .search-box {
    position: relative;
    width: 300px;
    margin-bottom: 20px;
  }

  .search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
  }

  .search-box input {
    width: 100%;
    padding: 10px 10px 10px 35px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.95rem;
    transition: all 0.3s;
  }

  .search-box input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
  }

  .commentaires-title {
    color: #2c3e50;
    margin-bottom: 25px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
  }

  /* Statistiques */
  .stats-row {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
  }

  .stat-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    min-width: 150px;
    flex: 1;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  }

  .stat-card i {
    font-size: 2rem;
    opacity: 0.8;
  }

  .stat-info {
    display: flex;
    flex-direction: column;
  }

  .stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .commentaires-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .commentaires-table thead th {
    background-color: #3498db;
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 500;
  }

  .commentaire-row {
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.2s;
  }

  .commentaire-row:hover {
    background-color: #f5f5f5;
  }

  .commentaire-row td {
    padding: 15px;
    vertical-align: middle;
  }

  .status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: capitalize;
    display: inline-block;
  }

  .status-badge.confirmed {
    background-color: #d4edda;
    color: #155724;
  }

  .status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
  }

  .status-badge.cancelled {
    background-color: #f8d7da;
    color: #721c24;
  }

  .actions-cell {
    display: flex;
    gap: 8px;
  }

  .btn-confirm,
  .btn-cancel {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    transition: all 0.2s;
  }

  .btn-confirm {
    background-color: #28a745;
    color: white;
  }

  .btn-confirm:hover {
    background-color: #218838;
  }

  .btn-cancel {
    background-color: #dc3545;
    color: white;
  }

  .btn-cancel:hover {
    background-color: #c82333;
  }

  @media (max-width: 768px) {
    .commentaires-container {
      padding: 15px;
    }

    .commentaires-table thead th,
    .commentaire-row td {
      padding: 10px 8px;
      font-size: 0.9rem;
    }

    .actions-cell {
      flex-direction: column;
      gap: 5px;
    }

    .btn-confirm,
    .btn-cancel {
      padding: 6px 8px;
      font-size: 0.8rem;
    }



  }
  .btn-icon {
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1.5rem;
    margin: 0 5px;
  }

  .btn-success {
    color: green;
  }

  .btn-danger {
    color: red;
  }

