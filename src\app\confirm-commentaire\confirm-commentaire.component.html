
<div class="commentaires-container">
  <h2 class="commentaires-title">Gestion des Commentaires</h2>

  <!-- Statistiques des commentaires -->
  <div class="stats-row mb-3">
    <div class="stat-card">
      <i class="fas fa-comments text-primary"></i>
      <div class="stat-info">
        <span class="stat-number">{{ commentaires.length }}</span>
        <span class="stat-label">Total</span>
      </div>
    </div>
    <div class="stat-card">
      <i class="fas fa-check-circle text-success"></i>
      <div class="stat-info">
        <span class="stat-number">{{ getPublishedCount() }}</span>
        <span class="stat-label">Publiés</span>
      </div>
    </div>
    <div class="stat-card">
      <i class="fas fa-clock text-warning"></i>
      <div class="stat-info">
        <span class="stat-number">{{ getPendingCount() }}</span>
        <span class="stat-label">En attente</span>
      </div>
    </div>
    <div class="stat-card">
      <i class="fas fa-times-circle text-danger"></i>
      <div class="stat-info">
        <span class="stat-number">{{ getRejectedCount() }}</span>
        <span class="stat-label">Rejetés</span>
      </div>
    </div>
  </div>

  <div class="search-box">
    <i class="fas fa-search"></i>
    <input type="text" placeholder="Rechercher un client..." [(ngModel)]="searchText">
  </div>




  <div class="table-responsive">
    <table class="commentaires-table">
      <thead>
        <tr class="table-header">
          <th>Client</th>
          <th>Hôtel</th>
          <th>Note</th>
          <th>Message</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr class="commentaire-row" *ngFor="let commentaire of commentaires">
          <ng-container *ngIf="commentaire.client.name.toLowerCase().includes(searchText.toLowerCase())">

          <td>{{ commentaire.client.name }}</td>
          <td>{{ commentaire.hotel.nom }}</td>
          <td>{{ commentaire.note }}</td>
          <td>{{ commentaire.message }}</td>
          <td>


            <span class="status-badge" [ngStyle]="getStatusStyle(commentaire.statut)" [class.confirmed]="commentaire.statut === 'confirmé'"
            [class.pending]="commentaire.statut === 'en attente'"
            [class.cancelled]="commentaire.statut === 'annulé'">
{{ commentaire.statut }}
</span>

            <td class="actions-cell">
              <button class="btn-icon btn-success"
              (click)="changerStatut(commentaire.id, 'confirmer')"
              [disabled]="commentaire.statut === 'publié'"
              [attr.title]="'Confirmer'">
        <i class="fas fa-check"></i> <!-- Check icon for Confirm -->
      </button>

      <button class="btn-icon btn-danger"
              (click)="changerStatut(commentaire.id, 'rejeter')"
              [disabled]="commentaire.statut === 'rejeter'"
              [attr.title]="'Rejeter'">
        <i class="fas fa-times"></i> <!-- Times icon for Reject -->
      </button>
      <button class="btn-icon btn-danger">
      <i class="fas fa-trash-alt text-danger icon-action" title="Delete" (click)="deleteComment(commentaire.id)"></i>
    </button>
            </td>
          </ng-container>


        </tr>
      </tbody>
    </table>
  </div>
</div>


<br>
<br>
<br>
<br>
<br>




















            <!-- <td>
            <button class="btn btn-success btn-sm"
                    (click)="changerStatut(commentaire.id, 'publié')"
                    [disabled]="commentaire.statut === 'publié'">
              Confirmer
            </button>
            <button class="btn btn-danger btn-sm"
                    (click)="changerStatut(commentaire.id, 'rejeté')"
                    [disabled]="commentaire.statut === 'rejeté'">
              Annuler
            </button>
          </td> -->
