/* Enhanced Base Styles */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --primary-light: #e0e7ff;
  --text-dark: #1e293b;
  --text-medium: #64748b;
  --text-light: #f8fafc;
  --bg-light: #f1f5f9;
  --bg-lighter: #f8fafc;
  --white: #ffffff;
  --error-bg: #fee2e2;
  --error-text: #b91c1c;
  --success-bg: #dcfce7;
  --success-text: #166534;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius: 0.75rem;
}

/* Enhanced Layout */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-lighter);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.sidebar {
  width: 280px;
  background-color: var(--white);
  box-shadow: var(--shadow-md);
  padding: 2rem 1.5rem;
  transition: var(--transition);
  z-index: 20;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  padding: 3rem;
  overflow-y: auto;
  background-color: var(--bg-light);
}

/* Enhanced Typography */
.sidebar-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--primary-light);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.sidebar-title::before {
  content: '';
  display: block;
  width: 24px;
  height: 24px;
  background-color: var(--primary-color);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z' /%3E%3Cpath d='M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75v4.5a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z' /%3E%3C/svg%3E");
  mask-repeat: no-repeat;
}

.dashboard-heading {
  font-size: 2.25rem;
  font-weight: 800;
  color: var(--text-dark);
  margin-bottom: 2.5rem;
  position: relative;
  display: inline-block;
}

.dashboard-heading::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 2px;
}

.stat-title, .chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-medium);
  margin-bottom: 1rem;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2.75rem;
  font-weight: 800;
  color: var(--primary-color);
  line-height: 1;
  margin: 1rem 0;
  position: relative;
  display: inline-block;
}

.stat-value::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: rgba(79, 70, 229, 0.2);
  border-radius: 2px;
}

/* Enhanced Navigation */
.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nav-link {
  padding: 0.875rem 1.25rem;
  border-radius: var(--border-radius);
  color: var(--text-medium);
  font-weight: 500;
  transition: var(--transition);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.nav-link:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
  transform: translateX(4px);
}

.nav-link.active {
  color: var(--white);
  background: linear-gradient(135deg, var(--primary-color), #6366f1);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
}

.nav-link i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
}

/* Enhanced Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.stat-card, .chart-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.chart-card {
  min-height: 400px;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-md);
}

.chart-card-header {
  margin-bottom: 1.5rem;
}

/* Enhanced Error Message */
.error-message {
  background-color: var(--error-bg);
  color: var(--error-text);
  padding: 1.25rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  border-left: 4px solid var(--error-text);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-sm);
}

.success-message {
  background-color: var(--success-bg);
  color: var(--success-text);
  padding: 1.25rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  border-left: 4px solid var(--success-text);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: var(--shadow-sm);
}

/* Chart Container */
.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

/* Loading State */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-content {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    padding: 1.5rem;
  }
  
  .main-content {
    padding: 1.5rem;
  }
  
  .sidebar-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .nav-link:hover {
    transform: translateY(2px);
  }
}

@media (max-width: 480px) {
  .stats-grid, .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .main-content {
    padding: 1.25rem;
  }
  
  .dashboard-heading {
    font-size: 1.75rem;
  }
  
  .stat-card, .chart-card {
    padding: 1.5rem;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: fadeIn 0.6s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }

.chart-card {
  animation: fadeIn 0.6s ease-out forwards;
}

.chart-card:nth-child(1) { animation-delay: 0.4s; }
.chart-card:nth-child(2) { animation-delay: 0.5s; }
.chart-card:nth-child(3) { animation-delay: 0.6s; }