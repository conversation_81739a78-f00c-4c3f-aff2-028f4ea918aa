import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Chart, registerables } from 'chart.js';
import { HotelsService } from 'src/services/hotels.service';
import { ReservationService } from 'src/services/reservations.service';
import { UserService } from 'src/services/user.service';
import { map } from 'rxjs/operators';
import { forkJoin } from 'rxjs';
import { StatisticsData } from 'src/models/staticdashbord';
import { dashbordSservice } from 'src/services/dashbordS.service';



@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  statistics: StatisticsData | null = null;
  error: string | null = null;

  // Chart configurations
  roomAvailabilityChartData: any;
  reservationsByDayChartData: any;
  reservationStatusChartData: any;
  //moin
  reservationsByMonthChartData: any;

  constructor(private dashboardService: dashbordSservice) {}

  ngOnInit(): void {
    this.loadStatistics();
  }

  loadStatistics(): void {
    this.dashboardService.getStatistics().subscribe({
      next: (data: StatisticsData) => {
        this.statistics = data;
        this.setupCharts();
      },
      error: (err) => {
        this.error = 'Failed to load statistics';
        console.error(err);
      }
    });
  }

  setupCharts(): void {
    if (!this.statistics) return;

    // Room Availability Chart
    this.roomAvailabilityChartData = {
      labels: ['Disponible', 'Réservée'],
      datasets: [{
        data: [
          this.statistics.chambre_stats.percentages.disponible,
          this.statistics.chambre_stats.percentages.reservee
        ],
        backgroundColor: ['#36A2EB', '#FF6384']
      }]
    };

    // Reservations by Day Chart
    this.reservationsByDayChartData = {
      labels: Object.keys(this.statistics.reservations_by_day),
      datasets: [{
        label: 'Réservations par jour',
        data: Object.values(this.statistics.reservations_by_day),
        backgroundColor: '#36A2EB'
      }]
    };

    // Reservations by Month Chart
    this.reservationsByMonthChartData = {
      labels: Object.keys(this.statistics.reservations_by_month),
      datasets: [
        {
          label: 'Réservations par mois',
          data: Object.values(this.statistics.reservations_by_month),
          backgroundColor: '#FFCE56',
        },
      ],
    };


    // Reservation Status Chart
    this.reservationStatusChartData = {
      labels: ['En attente', 'Confirmée', 'Annulée'],
      datasets: [{
        data: [
          this.statistics.reservation_status.en_attente,
          this.statistics.reservation_status.confirmee,
          this.statistics.reservation_status.annulee
        ],
        backgroundColor: ['#FFCE56', '#4BC0C0', '#FF6384']
      }]
    };
  }
  
}