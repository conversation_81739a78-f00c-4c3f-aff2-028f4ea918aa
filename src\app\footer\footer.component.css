/* Footer Styles */
.site-footer {
  background: linear-gradient(135deg, #2c3e50 30%, #233c89 100%);    height: 100%;
    color: #ecf0f1;
    padding: 60px 0 0;
    font-family: 'Poppins', sans-serif;
  }

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #fff;
    position: relative;
    padding-bottom: 10px;
  }

  .footer-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background: #3498db;
  }

  .footer-brand {
    margin-bottom: 30px;
  }

  .footer-slogan {
    margin: 15px 0;
    line-height: 1.6;
    color: #bdc3c7;
  }

  .footer-newsletter {
    margin-top: 25px;
  }

  .footer-newsletter h4 {
    font-size: 1rem;
    margin-bottom: 15px;
    color: #fff;
  }

  .newsletter-form {
    display: flex;
  }

  .newsletter-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 4px 0 0 4px;
    font-size: 0.9rem;
  }

  .newsletter-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0 15px;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background 0.3s;
  }

  .newsletter-button:hover {
    background: #2980b9;
  }

  .contact-list {
    list-style: none;
    padding: 0;
  }

  .contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    line-height: 1.5;
  }

  .contact-icon {
    color: #3498db;
    margin-right: 10px;
    margin-top: 3px;
    font-size: 1rem;
  }

  .links-list {
    list-style: none;
    padding: 0;
  }

  .links-list li {
    margin-bottom: 10px;
  }

  .links-list a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
    display: inline-block;
  }

  .links-list a:hover {
    color: #3498db;
    transform: translateX(5px);
  }

  .social-icons {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
  }

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    color: #fff;
    transition: all 0.3s;
  }

  .social-link:hover {
    background: #3498db;
    transform: translateY(-3px);
  }

  .payment-methods {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
  }

  .payment-icon {
    font-size: 1.8rem;
    color: #bdc3c7;
  }

  .footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #bdc3c7;
    font-size: 0.9rem;
  }

  .legal-links {
    margin-top: 10px;
    display: flex;
    gap: 15px;
  }

  .legal-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s;
  }

  .legal-links a:hover {
    color: #3498db;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .footer-grid {
      grid-template-columns: 1fr 1fr;
    }

    .footer-bottom {
      flex-direction: column;
    }
  }

  @media (max-width: 480px) {
    .footer-grid {
      grid-template-columns: 1fr;
    }

    .newsletter-form {
      flex-direction: column;
    }

    .newsletter-input {
      border-radius: 4px;
      margin-bottom: 10px;
    }

    .newsletter-button {
      border-radius: 4px;
      padding: 12px;
    }
  }
