.modern-header {
	background-color: #ffffff;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 1000;
  }
  
  .header-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
  }
  
  .header-nav-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 1rem 0;
  }
  
  .header-nav-logo .logo-img {
	height: 40px;
	transition: transform 0.3s ease;
  }
  
  .header-nav-logo .logo-img:hover {
	transform: scale(1.05);
  }
  
  .header-nav-lists {
	display: flex;
	gap: 2rem;
	list-style: none;
	align-items: center;
  }
  
  .header-nav-list {
	position: relative;
  }
  
  .header-nav-link {
	text-decoration: none;
	color: #333;
	font-weight: 500;
	font-size: 1rem;
	transition: color 0.3s ease;
	padding: 0.5rem 0;
  }
  
  .header-nav-link:hover {
	color: #0066cc;
  }
  
  .header-active {
	color: #0066cc;
	border-bottom: 2px solid #0066cc;
  }
  
  .btn {
	padding: 0.6rem 1.2rem;
	border-radius: 4px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	display: inline-block;
  }
  
  .btn-primary {
	background-color: #0066cc;
	color: white;
	border: none;
  }
  
  .btn-primary:hover {
	background-color: #0052a3;
	transform: translateY(-2px);
  }
  
  .btn-secondary {
	background-color: transparent;
	color: #0066cc;
	border: 1px solid #0066cc;
  }
  
  .btn-secondary:hover {
	background-color: rgba(0, 102, 204, 0.1);
  }
  
  .header-hamburger-icon {
	display: none;
	flex-direction: column;
	justify-content: space-between;
	width: 24px;
	height: 18px;
	cursor: pointer;
  }
  
  .header-hamburger-line {
	height: 2px;
	width: 100%;
	background-color: #333;
	transition: all 0.3s ease;
  }
  
  /* Responsive styles */
  @media (max-width: 768px) {
	.header-hamburger-icon {
	  display: flex;
	}
  
	.header-nav-lists {
	  position: fixed;
	  top: 70px;
	  left: -100%;
	  width: 80%;
	  height: calc(100vh - 70px);
	  background-color: white;
	  flex-direction: column;
	  align-items: center;
	  padding: 2rem 0;
	  gap: 1.5rem;
	  transition: left 0.3s ease;
	  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
	}
  
	.header-nav-lists.active {
	  left: 0;
	}
  
	.header-nav-link {
	  font-size: 1.2rem;
	}
  }
  