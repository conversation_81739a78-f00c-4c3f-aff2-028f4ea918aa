/* Style général */
.modern-header {
	background-color: #ffffff;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 1000;
  }
  
  .header-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
  }
  
  .header-nav-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 70px;
  }
  
  .logo-img {
	height: 40px;
  }
  
  /* Navigation */
  .header-nav-lists {
	display: flex;
	gap: 25px;
	list-style: none;
	margin: 0;
	padding: 0;
  }
  
  .header-nav-list {
	position: relative;
  }
  
  .header-nav-link {
	color: #333;
	text-decoration: none;
	font-weight: 500;
	padding: 10px 0;
	transition: color 0.3s ease;
  }
  
  .header-nav-link:hover {
	color: #0066cc;
  }
  
  .header-active {
	color: #0066cc;
	border-bottom: 2px solid #0066cc;
  }
  
  /* Boutons utilisateur */
  .user-actions {
	position: relative;
  }
  
  .btn-primary {
	background-color: #0066cc;
	color: white;
	padding: 8px 16px;
	border-radius: 4px;
	text-decoration: none;
	font-weight: 500;
	transition: background-color 0.3s ease;
  }
  
  .btn-primary:hover {
	background-color: #0052a3;
  }
  
  /* Profil utilisateur */
  .user-profile {
	display: flex;
	align-items: center;
	gap: 10px;
	cursor: pointer;
	position: relative;
  }
  
  .user-avatar i {
	font-size: 32px;
	color: #666;
  }
  
  .user-info {
	display: flex;
	align-items: center;
	gap: 5px;
  }
  
  .user-name {
	font-weight: 500;
  }
  
  .modern-header {
	background-color: #ffffff;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 1000;
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
  
  .header-container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
  }
  
  .header-nav-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 70px;
  }
  
  .header-nav-logo img {
	height: 40px;
  }
  
  .header-nav-lists {
	display: flex;
	gap: 25px;
	list-style: none;
	margin: 0;
	padding: 0;
  }
  
  .header-nav-list {
	position: relative;
  }
  
  .header-nav-link {
	color: #333;
	text-decoration: none;
	font-weight: 500;
	padding: 10px 0;
	transition: color 0.3s ease;
	font-size: 15px;
	
	&:hover {
	  color: #0066cc;
	}
  }
  
  .header-active {
	color: #0066cc;
	position: relative;
	
	&::after {
	  content: '';
	  position: absolute;
	  bottom: -2px;
	  left: 0;
	  width: 100%;
	  height: 2px;
	  background-color: #0066cc;
	}
  }
  
  .user-actions {
	position: relative;
  }
  
  .btn-primary {
	background-color: #0066cc;
	color: white;
	padding: 8px 16px;
	border-radius: 4px;
	text-decoration: none;
	font-weight: 500;
	transition: background-color 0.3s ease;
	font-size: 14px;
	
	&:hover {
	  background-color: #0052a3;
	}
  }
  
  .user-profile {
	position: relative;
  }
  
  .profile-trigger {
	display: flex;
	align-items: center;
	gap: 10px;
	cursor: pointer;
  }
  
  .user-avatar i {
	font-size: 32px;
	color: #666;
  }
  
  .user-info {
	display: flex;
	align-items: center;
	gap: 8px;
  }
  
  .user-name {
	font-weight: 500;
	font-size: 14px;
  }
  
  .dropdown-arrow {
	font-size: 12px;
	transition: transform 0.3s ease;
	
	&.rotated {
	  transform: rotate(180deg);
	}
  }
  
  .dropdown-menu {
	position: absolute;
	top: 100%;
	right: 0;
	background-color: white;
	border-radius: 6px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	width: 200px;
	padding: 8px 0;
	z-index: 100;
	margin-top: 10px;
  }
  
  .dropdown-item {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 10px 16px;
	color: #333;
	text-decoration: none;
	transition: all 0.2s ease;
	font-size: 14px;
	
	&:hover {
	  background-color: #f5f5f5;
	  color: #0066cc;
	}
	
	i {
	  width: 20px;
	  text-align: center;
	}
  }
  
  .dropdown-divider {
	height: 1px;
	background-color: #eee;
	margin: 5px 0;
  }
  
  @media (max-width: 768px) {
	.header-nav-lists {
	  display: none;
	}
	
	.header-nav-bar {
	  justify-content: space-between;
	}
  }