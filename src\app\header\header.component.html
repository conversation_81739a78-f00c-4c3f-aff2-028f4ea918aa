<header class="modern-header">
  <div class="header-container">
    <nav class="header-nav-bar">
      <div class="header-nav-logo">
        <a routerLink="/home">
          <img src="../../assets/img/logo.png" alt="star hotels logo" class="logo-img">
        </a>
      </div>

      <ul class="header-nav-lists">
        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/home" routerLinkActive="header-active">
            Accueil
          </a>
        </li>

        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/hotels" routerLinkActive="header-active">
            Hôtels
          </a>
        </li>

        <li class="header-nav-list" *ngIf="isLoggedIn">
          <a class="header-nav-link" routerLink="/ReservationClient" routerLinkActive="header-active">
            Mes Réservations
          </a>
        </li>


        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/about" routerLinkActive="header-active">
            About
          </a>
        </li>


        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/facilities" routerLinkActive="header-active">
            Équipements
          </a>
        </li>

      </ul>

     <div class="user-actions">
  <!-- Section utilisateur connecté -->
  <ng-container *ngIf="isLoggedIn; else loginTemplate">
    <div class="user-profile">
      <!-- Avatar utilisateur -->
      <div class="user-avatar" aria-label="Profil utilisateur">
        <i class="fas fa-user-circle"></i>
      </div>
      
      <!-- Infos utilisateur -->
      <div class="user-info">
        <span class="user-name">{{ userName }}</span>
        <button class="logout-btn" (click)="logout()" aria-label="Déconnexion">
          <i class="fas fa-sign-out-alt"></i>
          <span class="sr-only">Déconnexion</span>
        </button>
      </div>
    </div>
  </ng-container>

  <!-- Template pour les utilisateurs non connectés -->
  <ng-template #loginTemplate>
    <a class="btn btn-primary login-btn" routerLink="/register" aria-label="Connexion">
      Connexion
    </a>
  </ng-template>
</div>
    </nav>
  </div>
</header>