<header class="modern-header">
  <div class="header-container">
    <nav class="header-nav-bar">
      <div class="header-nav-logo">
        <a routerLink="/home">
          <img src="../../assets/img/logo.png" alt="star hotels logo" class="logo-img">
        </a>
      </div>

      <ul class="header-nav-lists">
        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/home" routerLinkActive="header-active">
            Accueil
          </a>
        </li>

        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/hotels" routerLinkActive="header-active">
            Hôtels
          </a>
        </li>

        <li class="header-nav-list" *ngIf="isLoggedIn">
          <a class="header-nav-link" routerLink="/ReservationClient" routerLinkActive="header-active">
            Mes Réservations
          </a>
        </li>

        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/facilities" routerLinkActive="header-active">
            Équipements
          </a>
        </li>

        <li class="header-nav-list">
          <a class="header-nav-link" routerLink="/about" routerLinkActive="header-active">
            About
          </a>
        </li>
      </ul>

      <div class="user-actions">
        <ng-container *ngIf="isLoggedIn; else loginBtn">
          <div class="user-profile">
            <div class="user-avatar">
              <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-info">
              <span class="user-name">{{ userName }}</span>
              <a class="logout-link" (click)="logout()">
                <i class="fas fa-sign-out-alt"></i> 
                <!-- Déconnexion -->
              </a>
            </div>
          </div>
        </ng-container>

        <ng-template #loginBtn>
          <a class="btn btn-primary" routerLink="/register">
            <!-- <i class="fas fa-sign-in-alt"></i>  --> 
             Connexion
          </a>
        
        </ng-template>
      </div>
    </nav>
  </div>
</header>