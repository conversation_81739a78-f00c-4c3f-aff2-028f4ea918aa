import { Component, OnInit, AfterViewInit, ElementRef, ViewChild } from '@angular/core';
import { AuthServiceService } from 'src/services/auth-service.service';
import { CommentaireService } from 'src/services/commentaire.service';
import { Commentaire } from 'src/models/Commentaire.model';
import { Hotel } from 'src/models/Hotel.model';
import { HotelsService } from 'src/services/hotels.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit, AfterViewInit {
  isLoggedIn: boolean = false;
  userName: string | null = '';
  commentaires: Commentaire[] = [];

  //hotels
  hotels: Hotel[] = [];




  @ViewChild('heroSection') heroSectionRef!: ElementRef;

  constructor(
    public authService: AuthServiceService,
    private commentaireService: CommentaireService,
    private hotelsService: HotelsService
  ) {}

  ngOnInit(): void {
    this.authService.isLoggedIn$.subscribe((status) => {
      this.isLoggedIn = status;
      this.userName = status ? this.authService.getUserName() : null;
      console.log('Utilisateur connecté :', this.userName);  // Vérifie l'utilisateur
      this.loadHotels();
      this.loadCommentaires();
    });
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      const section = this.heroSectionRef?.nativeElement;
      if (section) {
        section.classList.add('visible');
      } else {
        console.warn('heroSection non trouvé dans le DOM.');
      }
    });
  }
//hotels
loadHotels() {
  this.hotelsService.getAllHotel().subscribe((res: any) => {
    if (res && res.hotels && Array.isArray(res.hotels)) {
      this.hotels = res.hotels;
    } else {
      console.error('Unexpected hotel data format', res);
      this.hotels = [];
    }
  });
}

  loadCommentaires(): void {
    console.log('Chargement des commentaires publiés...');

    // Utiliser la nouvelle méthode pour récupérer uniquement les commentaires publiés
    this.commentaireService.getPublishedCommentaires().subscribe({
      next: (data: any) => {
        console.log('Commentaires publiés reçus:', data);

        // Vérifiez si data est un tableau ou contient un tableau
        if (Array.isArray(data)) {
          // Filtrer côté client au cas où le backend n'aurait pas filtré
          this.commentaires = data.filter((comment: any) =>
            comment.statut === 'publie' || comment.statut === 'publié'
          );
        } else if (data && Array.isArray(data.commentaires)) {
          // Si l'API retourne un objet avec une propriété 'commentaires'
          this.commentaires = data.commentaires.filter((comment: any) =>
            comment.statut === 'publie' || comment.statut === 'publié'
          );
        } else {
          console.error('Format de données inattendu:', data);
          this.commentaires = [];
        }

        console.log(`${this.commentaires.length} commentaires publiés chargés`);
      },
      error: (err) => {
        console.error('Erreur lors du chargement des commentaires publiés', err);
        // En cas d'erreur avec la nouvelle route, fallback vers l'ancienne méthode avec filtrage
        this.loadCommentairesWithFallback();
      }
    });
  }

  // Méthode de fallback si la route /published n'existe pas encore
  private loadCommentairesWithFallback(): void {
    console.log('Fallback: chargement de tous les commentaires avec filtrage...');

    this.commentaireService.getAllCommentaires().subscribe({
      next: (data: any) => {
        console.log('Tous les commentaires reçus:', data);

        let allComments: any[] = [];

        // Vérifiez si data est un tableau ou contient un tableau
        if (Array.isArray(data)) {
          allComments = [...data];
        } else if (data && Array.isArray(data.commentaires)) {
          allComments = [...data.commentaires];
        } else {
          console.error('Format de données inattendu:', data);
          this.commentaires = [];
          return;
        }

        // Filtrer pour ne garder que les commentaires publiés
        this.commentaires = allComments.filter((comment: any) =>
          comment.statut === 'publie' || comment.statut === 'publié'
        );

        console.log(`${this.commentaires.length} commentaires publiés sur ${allComments.length} total`);
      },
      error: (err) => {
        console.error('Erreur lors du chargement des commentaires', err);
        this.commentaires = [];
      }
    });
  }
}
