/* Structure de base */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8fafc;
}

.sidebar-fixed {
  width: 280px;
  min-height: 100vh;
  position: sticky;
  top: 0;
  background-color: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.main-content-wrapper {
  flex: 1;
  padding: 30px;
  overflow-x: auto;
}

/* Conteneur principal */
.hotels-container {
  max-width: 100%;
  margin: 0;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
}

.header-section {
  margin-bottom: 30px;
}

.hotels-title {
  color: #2c3e50;
  margin-bottom: 20px;
  font-weight: 600;
  font-size: 1.8rem;
  position: relative;
  display: inline-block;
}

.hotels-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: #3498db;
  border-radius: 2px;
}

/* Barre d'actions */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 25px;
}

.search-box {
  position: relative;
  flex-grow: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.1rem;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f8fafc;
}

.search-box input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  background-color: white;
}

.btn-add {
  background-color: #4CAF50;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-add:hover {
  background-color: #3e8e41;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Tableau */
.hotels-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
}

.table-header {
  background-color: #3498db;
  color: white;
  position: sticky;
  top: 0;
}

.table-header th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 500;
  font-size: 0.95rem;
}

.text-center {
  text-align: center;
}

.hotel-row {
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s;
}

.hotel-row:last-child {
  border-bottom: none;
}

.hotel-row:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.hotel-row td {
  padding: 14px 12px;
  vertical-align: middle;
  font-size: 0.95rem;
}

/* Cellules spéciales */
.description-cell, .equipements-cell {
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Badge étoiles */
.stars-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  background-color: #fff3cd;
  color: #856404;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Boutons */
.btn-details {
  padding: 8px 16px;
  border: 1px solid #17a2b8;
  border-radius: 6px;
  cursor: pointer;
  background-color: #eafafc;
  color: #138496;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-details:hover {
  background-color: #d1f0f4;
  color: #117a8b;
  border-color: #117a8b;
}

/* Icônes pour les offres */
.offer-eye-icon {
  cursor: pointer;
  color: #28a745;
  font-size: 1.4rem;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(40, 167, 69, 0.1);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.offer-eye-icon:hover {
  background-color: rgba(40, 167, 69, 0.2);
  color: #1e7e34;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.no-offer-icon {
  cursor: pointer;
  color: #dc3545;
  font-size: 1.4rem;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(220, 53, 69, 0.1);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.no-offer-icon:hover {
  background-color: rgba(220, 53, 69, 0.2);
  color: #c82333;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* Icône pour ajouter des offres */
.add-offer-icon {
  cursor: pointer;
  color: #28a745;
  font-size: 1.4rem;
  padding: 8px;
  border-radius: 50%;
  background-color: rgba(40, 167, 69, 0.1);
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.add-offer-icon:hover {
  background-color: rgba(40, 167, 69, 0.2);
  color: #1e7e34;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* Actions */
.actions-cell {
  min-width: 150px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.icon-action {
  font-size: 1.3rem;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 50%;
}

.fa-edit {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.fa-trash-alt {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.icon-action:hover {
  transform: scale(1.1);
  opacity: 1;
}

.fa-edit:hover {
  background-color: rgba(52, 152, 219, 0.2);
}

.fa-trash-alt:hover {
  background-color: rgba(231, 76, 60, 0.2);
}

.btn-icon {
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1.3rem;
  color: #6c757d;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 50%;
}

.btn-icon:not([disabled]):hover {
  color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.1);
  transform: scale(1.1);
}

.btn-icon[disabled] {
  color: #adb5bd;
  cursor: not-allowed;
}

/* Aucun résultat */
.no-results {
  text-align: center;
  padding: 50px 20px;
  color: #6c757d;
}

.no-results i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #dee2e6;
}

.no-results h4 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.no-results p {
  font-size: 1rem;
  margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 1200px) {
  .description-cell, .equipements-cell {
    max-width: 200px;
  }
}

@media (max-width: 992px) {
  .sidebar-fixed {
    width: 240px;
  }

  .main-content-wrapper {
    padding: 20px;
  }

  .hotels-container {
    padding: 20px;
  }

  .action-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-box {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .sidebar-fixed {
    width: 100%;
    min-height: auto;
    position: relative;
  }

  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .hotels-table {
    min-width: 800px;
  }

  .action-buttons {
    gap: 10px;
  }
}

@media (max-width: 576px) {
  .main-content-wrapper {
    padding: 15px;
  }

  .hotels-container {
    padding: 15px;
  }

  .hotels-title {
    font-size: 1.5rem;
  }

  .btn-add {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
}