<div class="dashboard-layout">
  <!-- Sidebar fixe -->
  <app-sidebar class="sidebar-fixed"></app-sidebar>

  <!-- Contenu principal -->
  <div class="main-content-wrapper">
    <div class="hotels-container">
      <div class="header-section">
        <h2 class="hotels-title">Liste des Hôtels</h2>

        <div class="action-bar">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="Rechercher un hôtel..." [(ngModel)]="searchText">
          </div>

          <button (click)="openModalForAdd()" class="btn-add">
            <i class="fas fa-plus-circle"></i> Ajouter un hôtel
          </button>
        </div>
      </div>

      <div class="table-responsive">
        <table class="hotels-table">
          <thead>
            <tr class="table-header">
              <th>Nom</th>
              <th>Ville</th>
              <th>Description</th>
              <th class="text-center">Étoiles</th>
              <th>Equipements</th>
              <th class="text-center">Chambres</th>
              <th class="text-center">Offre</th>
              <th class="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hotel-row" *ngFor="let hotel of filteredHotels()">
              <td>{{ hotel.nom }}</td>
              <td>{{ hotel.ville }}</td>
              <td class="description-cell">{{ hotel.description  }}</td>

              <td class="text-center">
                <span class="stars-badge">
                  <i class="fas fa-star"></i> {{ hotel.nbre_etoiles }}
                </span>
              </td>

              <td class="equipements-cell">
                {{ hotel.equipements  }}
              </td>

              <td class="text-center">
                <a [routerLink]="['/List-hotel-chambre', hotel.id]" class="btn-details">
                  Chambres
                </a>
              </td>

              <td class="text-center">
                <!-- Offre active : icône œil pour voir les détails -->
                <span *ngIf="hotel.hasOffer && !hotel.isOfferExpired">
                  <i class="fas fa-eye offer-eye-icon"
                     title="Voir les détails de l'offre"
                     (click)="showOfferDetails(hotel.id!)"></i>
                </span>

                <!-- Offre expirée : icône plus pour remplacer -->
                <span *ngIf="hotel.isOfferExpired">
                  <i class="fas fa-plus-circle add-offer-icon"
                     title="Offre expirée - Ajouter une nouvelle offre"
                     (click)="openOfferModal(hotel.id!, hotel.nom)"></i>
                </span>

                <!-- Aucune offre : icône X -->
                <span *ngIf="!hotel.hasOffer && !hotel.isOfferExpired">
                  <i class="fas fa-times-circle no-offer-icon"
                     title="Aucune offre - Cliquer pour ajouter"
                     (click)="openOfferModal(hotel.id!, hotel.nom)"></i>
                </span>
              </td>

              <td class="actions-cell">
                <div class="action-buttons">
                  <i class="fas fa-edit text-primary icon-action" title="Modifier" (click)="editHotel(hotel)"></i>

                  <i class="fas fa-trash-alt text-danger icon-action" title="Supprimer"
                     *ngIf="hotel.id !== undefined" (click)="deleteHotel(hotel.id!)"></i>

                  <button class="btn-icon" [disabled]="hotel.hasOffer"
                          [attr.title]="hotel.hasOffer ? 'Offre déjà appliquée' : 'Ajouter une offre'"
                          (click)="openModalForAddOffer(hotel.id!, hotel.nom)">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div *ngIf="filteredHotels().length === 0" class="no-results">
          <i class="fas fa-hotel fa-3x"></i>
          <h4>Aucun hôtel disponible</h4>
          <p>Commencez par ajouter un nouvel hôtel</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour afficher les détails de l'offre -->
<div class="modal fade" id="offreDetailsModal" tabindex="-1" aria-labelledby="offreDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="offreDetailsModalLabel">
          <i class="fas fa-gift text-primary"></i> Détails de l'offre
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" *ngIf="selectedOffre">
        <div class="row">
          <div class="col-md-6">
            <h6><i class="fas fa-tag"></i> Titre</h6>
            <p class="text-muted">{{ selectedOffre.titre }}</p>
          </div>
          <div class="col-md-6">
            <h6><i class="fas fa-percentage"></i> Remise</h6>
            <p class="text-success fw-bold">{{ selectedOffre.valeur_remise }}%</p>
          </div>
        </div>
        <div class="row">
          <div class="col-12">
            <h6><i class="fas fa-info-circle"></i> Description</h6>
            <p class="text-muted">{{ selectedOffre.description }}</p>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <h6><i class="fas fa-calendar-alt"></i> Date de fin</h6>
            <p class="text-warning">{{ selectedOffre.date_fin_promo | date:'dd/MM/yyyy' }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour ajouter/modifier un hôtel -->
<div class="modal fade" id="hotelModal" tabindex="-1" aria-labelledby="hotelModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="hotelModalLabel">
          <i class="fas" [class.fa-plus-circle]="!editMode" [class.fa-edit]="editMode"></i>
          {{ editMode ? 'Modifier l\'hôtel' : 'Ajouter un hôtel' }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="hotelNom" class="form-label">Nom de l'hôtel</label>
              <input type="text" class="form-control" id="hotelNom" [(ngModel)]="newHotel.nom" name="nom" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="hotelVille" class="form-label">Ville</label>
              <input type="text" class="form-control" id="hotelVille" [(ngModel)]="newHotel.ville" name="ville" required>
            </div>
          </div>
          <div class="mb-3">
            <label for="hotelDescription" class="form-label">Description</label>
            <textarea class="form-control" id="hotelDescription" rows="3" [(ngModel)]="newHotel.description" name="description"></textarea>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="hotelEtoiles" class="form-label">Nombre d'étoiles</label>
              <select class="form-control" id="hotelEtoiles" [(ngModel)]="newHotel.nbre_etoiles" name="nbre_etoiles">
                <option value="1">1 étoile</option>
                <option value="2">2 étoiles</option>
                <option value="3">3 étoiles</option>
                <option value="4">4 étoiles</option>
                <option value="5">5 étoiles</option>
              </select>
            </div>
            <div class="col-md-6 mb-3">
              <label for="hotelImage" class="form-label">URL de l'image</label>
              <input type="url" class="form-control" id="hotelImage" [(ngModel)]="newHotel.image_hotel" name="image_hotel">
            </div>
          </div>
          <div class="mb-3">
            <label for="hotelEquipements" class="form-label">Équipements</label>
            <textarea class="form-control" id="hotelEquipements" rows="2" [(ngModel)]="newHotel.equipements" name="equipements" placeholder="Ex: WiFi, Piscine, Spa..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" (click)="saveHotel()">
          <i class="fas fa-save"></i> {{ editMode ? 'Modifier' : 'Ajouter' }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal pour ajouter une offre -->
<div class="modal fade" id="offreModal" tabindex="-1" aria-labelledby="offreModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="offreModalLabel">
          <i class="fas fa-gift"></i> Ajouter une offre pour {{ hotelNom }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label for="offreTitre" class="form-label">Titre de l'offre</label>
            <input type="text" class="form-control" id="offreTitre" [(ngModel)]="offre.titre" name="titre" required>
          </div>
          <div class="mb-3">
            <label for="offreDescription" class="form-label">Description</label>
            <textarea class="form-control" id="offreDescription" rows="3" [(ngModel)]="offre.description" name="description"></textarea>
          </div>
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="offreRemise" class="form-label">Valeur de remise (%)</label>
              <input type="number" class="form-control" id="offreRemise" [(ngModel)]="offre.valeur_remise" name="valeur_remise" min="0" max="100" required>
            </div>
            <div class="col-md-6 mb-3">
              <label for="offreDateFin" class="form-label">Date de fin</label>
              <input type="date" class="form-control" id="offreDateFin"
                     [(ngModel)]="offre.date_fin_promo"
                     name="date_fin_promo"
                     [min]="getTomorrowDate()"
                     required>
              <small class="form-text text-muted">La date doit être dans le futur</small>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" (click)="submitOffre()">
          <i class="fas fa-save"></i> Ajouter l'offre
        </button>
      </div>
    </div>
  </div>
</div>