/* Main Container */
.hotel-listing {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Hero Section */
.divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 2px;
}

/* Hotel Card */
.hotel-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.hotel-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.hotel-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.hotel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.hotel-card:hover .hotel-image {
  transform: scale(1.05);
}

.rating-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0,0,0,0.7);
  color: #f1c40f;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.hotel-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.hotel-header {
  margin-bottom: 15px;
}

.hotel-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.hotel-location {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.hotel-location i {
  color: #e74c3c;
  margin-right: 5px;
}

.hotel-description {
  color: #7f8c8d;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 15px;
}

.hotel-features {
  list-style: none;
  padding: 0;
  margin: 15px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.hotel-features li {
  background: #f1f8fe;
  color: #3498db;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
}

.hotel-features li i {
  margin-right: 5px;
  font-size: 0.9rem;
}

.hotel-footer {
  margin-top: auto;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.details-btn, .book-btn {
  flex: 1;
  padding: 8px 15px;
  border-radius: 6px;
  font-size: 0.9rem;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.details-btn {
  background: white;
  color: #3498db;
  border: 1px solid #3498db;
}

.details-btn:hover {
  background: #f1f8fe;
  color: #2980b9;
}

.book-btn {
  background: linear-gradient(135deg, #3498db, #2ecc71);
  color: white;
  border: none;
}

.book-btn:hover {
  background: linear-gradient(135deg, #2980b9, #27ae60);
  color: white;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
}

/* Empty State */
.empty-state {
  background: white;
  border-radius: 12px;
  padding: 40px 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.empty-icon {
  font-size: 3rem;
  color: #bdc3c7;
}

/* Responsive */
@media (max-width: 768px) {
  .hotel-footer {
    flex-direction: column;
  }

  .details-btn, .book-btn {
    width: 100%;
  }
}
/* Filter Form Styles */
.filter-form {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  margin-bottom: 30px;
}

.filter-label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-control, .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 0.9rem;
  height: auto;
}

.form-control:focus, .form-select:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  font-size: 0.85rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-secondary {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  color: #495057;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  border-color: #dae0e5;
}

.btn-primary {
  background-color: #3498db;
  border: none;
}

.btn-primary:hover {
  background-color: #2980b9;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-form .col-md-4,
  .filter-form .col-md-2 {
    margin-bottom: 15px;
  }

  .filter-form .col-12.text-end {
    text-align: center !important;
  }

  .btn {
    width: 100%;
    margin-bottom: 10px;
  }

  .btn.ms-2 {
    margin-left: 0 !important;
  }
}
