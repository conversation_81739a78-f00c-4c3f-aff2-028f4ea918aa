
<app-header></app-header>
<main class="hotel-listing">
  <section class="container py-5">
    <!-- Hero Section -->
    <div class="text-center mb-5">
      <h2 class="fw-bold display-5 text-primary mb-3">Nos Hôtels</h2>
      <div class="divider mx-auto"></div>
      <p class="lead text-muted mt-3">
        Découvrez nos établissements exclusifs et profitez d'un confort exceptionnel avec vue panoramique
      </p>
    </div>
    <form class="row g-3 mb-4 filter-form" (ngSubmit)="applyFilters()" #filterForm="ngForm">
      <div class="col-md-4">
        <label class="filter-label">Nom de l'hôtel</label>
        <input type="text" class="form-control" placeholder="Ex: Hôtel Moradi" [(ngModel)]="filters.nom" name="nom">
      </div>
      <div class="col-md-4">
        <label class="filter-label">Ville</label>
        <input type="text" class="form-control" placeholder="Ex: Sousse" [(ngModel)]="filters.ville" name="ville">
      </div>
      <div class="col-md-2">
        <label class="filter-label">Classement</label>
        <select class="form-control" [(ngModel)]="filters.nbre_etoiles" name="nbre_etoiles">
          <option value="">Toutes les étoiles</option>
          <option *ngFor="let star of [1,2,3,4,5]" [value]="star">{{ star }} étoile(s)</option>
        </select>
      </div>
      <div class="col-md-2 d-flex align-items-end">
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-filter me-1"></i>Filtrer
        </button>
        <button type="button" class="btn btn-secondary me-2" (click)="resetFilters()">
          <i class="fas fa-undo me-1"></i>Réinit
        </button>
      </div>
    </form>
    <!-- Hotel Cards Section -->
    <section class="hotel-cards-section">
      <div class="row g-4">
        <!-- Hotel Card -->
        <div *ngFor="let hotel of hotels" class="col-12 col-md-6 col-lg-4">
          <div class="hotel-card">
            <div class="hotel-image-container">
              <img [src]="hotel.image_hotel || 'assets/img/hotel-default.jpg'"
                   alt="{{ hotel.nom }}"
                   class="hotel-image"
                   onerror="this.src='assets/img/hotel-default.jpg'">
              <div class="rating-badge">
                <i class="fas fa-star"></i> {{ hotel.nbre_etoiles }}
              </div>
            </div>
            <div class="hotel-content">
              <div class="hotel-header">
                <h3 class="hotel-title">{{ hotel.nom }}</h3>
                <p class="hotel-location">
                  <i class="fas fa-map-marker-alt"></i> {{ hotel.ville }}
                </p>
              </div>
              <p class="hotel-description">{{ hotel.description}}</p>
              <ul class="hotel-features">
                <li *ngFor="let equipement of hotel.equipements">
                  <i class="fas fa-check-circle text-success me-2"></i> {{ equipement }}
                </li>
              </ul>
              <div class="hotel-footer">
                <a [routerLink]="['/rooms', hotel.id]" class="details-btn">
                  <i class="fas fa-info-circle"></i> Détails
                </a>
                <a  class="book-btn" (click)="openCommentModal(hotel)">
                  <i class="fas fa-comment"></i> Commenter
                </a>
              </div>
            </div>
          </div>
        </div>
        <!-- Empty State -->
        <div *ngIf="hotels.length === 0" class="col-12">
          <div class="empty-state text-center py-5">
            <i class="fas fa-hotel empty-icon"></i>
            <h4 class="mt-3">Aucun hôtel disponible</h4>
            <p class="text-muted">Nous n'avons trouvé aucun hôtel correspondant à vos critères.</p>
            <a href="/" class="btn btn-outline-primary mt-3">
              <i class="fas fa-arrow-left"></i> Retour à l'accueil
            </a>
          </div>
        </div>
      </div>
    </section>
    <!-- Comment Modal -->
    <div class="modal fade" id="commentaireModal" tabindex="-1" aria-labelledby="commentaireModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="commentaireModalLabel">Ajouter un commentaire</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form #commentForm="ngForm" (ngSubmit)="submitCommentaire()">
              <div class="mb-3">
                <label for="commentMessage" class="form-label">Votre commentaire</label>
                <textarea class="form-control" id="commentMessage" rows="4" [(ngModel)]="nouveauCommentaire.message" name="message" required></textarea>
              </div>
              <div class="mb-3">
                <label for="commentNote" class="form-label">Note (1-5)</label>
                <select class="form-control" id="commentNote" [(ngModel)]="nouveauCommentaire.note" name="note" required>
                  <option value="">Sélectionnez une note</option>
                  <option value="1">1</option> 
                  <option value="1">2</option> 
                  <option value="1">3</option> 
                  <option value="1">4</option>
                  <option value="1">5</option> 
                </select>
              </div>
              <div class="text-end">
                <button type="submit" class="btn btn-primary" [disabled]="!commentForm.valid">
                  <i class="fas fa-paper-plane me-1"></i> Envoyer
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
</main> 
