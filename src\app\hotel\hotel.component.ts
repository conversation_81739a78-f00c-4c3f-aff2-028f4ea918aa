import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Commentaire } from 'src/models/Commentaire.model';
import { Hotel, HotelResponse } from 'src/models/Hotel.model';
import { User } from 'src/models/User.model';
import { CommentaireService } from 'src/services/commentaire.service';
import { HotelsService } from 'src/services/hotels.service';
import { OffreService } from 'src/services/offre.service';
import { AuthServiceService } from 'src/services/auth-service.service'; // Import AuthService
declare var bootstrap: any;

@Component({
  selector: 'app-hotel',
  templateUrl: './hotel.component.html',
  styleUrls: ['./hotel.component.css']
})
export class HotelComponent implements OnInit {
  hotels: Hotel[] = [];
  nouveauCommentaire: Commentaire = {
    id: 0,
    note: 0,
    message: '',
    statut: 'en_attente',
    client: {} as User,
    hotel: {} as Hotel
  };
  filters: any = {
    nom: '',
    ville: '',
    nbre_etoiles: ''
  };

  constructor(
    private hotelService: HotelsService,
    private route: ActivatedRoute,
    private offreService: OffreService,
    private router: Router,
    private commentaireService: CommentaireService,
    private authService: AuthServiceService // Inject AuthService
  ) {}

  ngOnInit(): void {
    this.loadHotels();
  }

  loadHotels() {
    this.hotelService.getAllHotel().subscribe((res: HotelResponse) => {
      console.log("Réponse de getAllHotel:", res);
      if (res && res.hotels && Array.isArray(res.hotels)) {
        this.hotels = res.hotels;
      } else {
        console.error("La réponse ne contient pas un tableau d'hôtels:", res);
        this.hotels = [];
      }
    });
  }

  openCommentModal(hotel: Hotel) {
    this.nouveauCommentaire = {
      id: 0,
      note: 0,
      message: '',
      statut: 'en_attente',
      client: {} as User,
      hotel: hotel
    };
    const modal = new bootstrap.Modal(document.getElementById('commentaireModal')!);
    modal.show();
  }

  submitCommentaire() {
    // Vérifier si l'utilisateur est connecté
    // if (!this.authService.isAuthenticated()) {
    //   // Redirigez vers la page d'inscription
    //   this.router.navigate(['/register']);
    //   return;
    // }
    const userId = this.authService.getUserId();
    if (!userId) {
      alert('Veuillez vous connecter pour laisser un commentaire');
      this.router.navigate(['/register']);
      return;
    }

    // Transformer l'objet pour correspondre aux attentes du backend
    const commentToSend = {
      note: this.nouveauCommentaire.note,
      message: this.nouveauCommentaire.message,
      statut: this.nouveauCommentaire.statut,
      client_id: parseInt(userId, 10), // Convertir user_id en nombre
      hotel_id: this.nouveauCommentaire.hotel.id
    };
    console.log('Payload envoyé:', commentToSend); // Pour déboguer
    this.commentaireService.addComment(commentToSend).subscribe({
      next: () => {
        alert('Commentaire ajouté avec succès');
        bootstrap.Modal.getInstance(document.getElementById('commentaireModal')!)?.hide();
        this.nouveauCommentaire = {
          id: 0,
          note: 0,
          message: '',
          statut: 'en_attente',
          client: {} as User,
          hotel: {} as Hotel
        };
      },
      error: (err) => {
        console.error('Erreur HTTP:', err);
        let errorMessage = 'Erreur lors de l\'ajout du commentaire';
        if (err.status === 500) {
          errorMessage = 'Erreur serveur interne. Veuillez réessayer plus tard.';
        } else if (err.status === 422 && err.error.errors) {
          errorMessage = 'Erreurs de validation: ' + Object.values(err.error.errors).flat().join(', ');
        } else if (err.status === 401) {
          errorMessage = 'Veuillez vous connecter pour laisser un commentaire';
          this.router.navigate(['/login']);
        }
        alert(errorMessage);
      }
    });
  }

  applyFilters() {
    this.hotelService.filterHotels(this.filters).subscribe((res: HotelResponse) => {
      if (res && res.hotels) {
        this.hotels = res.hotels;
      } else {
        this.hotels = [];
      }
    }, error => {
      console.error("Erreur lors du filtrage :", error);
    });
  }

  resetFilters() {
    this.filters = {
      nom: '',
      ville: '',
      nbre_etoiles: ''
    };
    this.loadHotels();
  }
}