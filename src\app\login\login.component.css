@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

.parent {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
}

.container {
    background-color: #fff;
    border-radius: 24px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    width: 900px;
    max-width: 100%;
    min-height: 600px;
    display: flex;
}

.container p {
    font-size: 15px;
    line-height: 1.6;
    color: #fff;
    margin: 15px 0;
    font-weight: 400;
}

.container span {
    font-size: 14px;
    color: #fff;
    display: block;
    margin: 10px 0;
}

.container a {
    color: #2c3e50;
    font-size: 14px;
    text-decoration: none;
    margin: 10px 0;
    transition: all 0.3s;
    font-weight: 500;
}

.container a:hover {
    color: #2c3e50;
    text-decoration: underline;
}

/* Boutons */
.container button {
    background: linear-gradient(135deg, #4a61a6 0%, #2c3e50 100%);
    color: #fff;
    font-size: 14px;
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
    text-transform: uppercase;
    width: 100%;
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.25);
}

.container button:hover {
    background: linear-gradient(135deg, #2c3e50 0%, #224abe 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 115, 223, 0.35);
}

.container button:active {
    transform: translateY(0);
}

/* Boutons secondaires (toggle) */
.container button.hidden {
    background: transparent;
    border: 2px solid #fff;
    color: #fff;
    padding: 10px 30px;
    margin-top: 25px;
    box-shadow: none;
    width: auto;
}

.container button.hidden:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.container form {
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 60px;
    height: 100%;
    width: 100%;
}

.container input {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    margin: 12px 0;
    padding: 14px 20px;
    font-size: 15px;
    border-radius: 8px;
    width: 100%;
    outline: none;
    transition: all 0.3s;
    color: #495057;
    font-weight: 400;
}

.container input::placeholder {
    color: #2c3e50;
    font-weight: 400;
}

.container input:focus {
    border-color: #2c3e50;
    box-shadow: 0 0 0 3px rgba(78, 115, 223, 0.15);
    background-color: #fff;
}

.form-container {
    position: absolute;
    top: 0;
    height: 100%;
    transition: all 0.6s ease-in-out;
}

.sign-in {
    left: 0;
    width: 50%;
    z-index: 2;
}

.container.active .sign-in {
    transform: translateX(100%);
}

.sign-up {
    left: 0;
    width: 50%;
    opacity: 0;
    z-index: 1;
}

.container.active .sign-up {
    transform: translateX(100%);
    opacity: 1;
    z-index: 5;
    animation: move 0.6s;
}

@keyframes move {
    0%, 49.99% {
        opacity: 0;
        z-index: 1;
    }
    50%, 100% {
        opacity: 1;
        z-index: 5;
    }
}

.social-icons {
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons a {
    border: 1px solid #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: #6c757d;
    transition: all 0.3s;
    background-color: #f8f9fa;
}

.social-icons a:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.fa-google:hover { color: #DB4437; background-color: rgba(219, 68, 55, 0.1); border-color: #DB4437; }
.fa-facebook-f:hover { color: #4267B2; background-color: rgba(66, 103, 178, 0.1); border-color: #4267B2; }
.fa-github:hover { color: #333; background-color: rgba(51, 51, 51, 0.1); border-color: #333; }
.fa-linkedin-in:hover { color: #0077B5; background-color: rgba(0, 119, 181, 0.1); border-color: #0077B5; }

.toggle-container {
    position: absolute;
    top: 0;
    left: 50%;
    width: 50%;
    height: 100%;
    overflow: hidden;
    transition: all 0.6s ease-in-out;
    border-radius: 20px 0 0 20px;
    z-index: 1000;
}

.container.active .toggle-container {
    transform: translateX(-100%);
    border-radius: 0 20px 20px 0;
}

.toggle {
  background: linear-gradient(135deg, #2c3e50 30%, #233c89 100%);    height: 100%;
    width: 200%;
    position: relative;
    left: -100%;
    transition: all 0.6s ease-in-out;
}

.container.active .toggle {
    transform: translateX(50%);
}

.toggle-panel {
    position: absolute;
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 0 50px;
    text-align: center;
    color: white;
}

.toggle-panel h1 {
    font-size: 28px;
    margin-bottom: 20px;
    color: white;
    font-weight: 700;
}

.toggle-left {
    transform: translateX(-200%);
}

.toggle-right {
    right: 0;
    transform: translateX(0);
}

.container.active .toggle-left {
    transform: translateX(0);
}

.container.active .toggle-right {
    transform: translateX(200%);
}

#properties {
    color: #2e59d9;
    font-weight: 700;
    margin: 20px 0;
    font-size: 28px;
}

/* Ajout d'icônes dans les inputs */
.input-group {
    position: relative;
    width: 100%;
}

.input-group i {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    color: #adb5bd;
    font-size: 16px;
}

.input-group input {
    padding-left: 45px;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        min-height: auto;
        flex-direction: column;
        width: 95%;
    }

    .sign-in,
    .sign-up {
        width: 100%;
        padding: 40px 30px;
    }

    .toggle-container {
        display: none;
    }

    .container.active .sign-in,
    .container.active .sign-up {
        transform: none;
    }

    #properties {
        font-size: 24px;
    }

    .container input {
        padding: 12px 15px 12px 40px;
    }
}
