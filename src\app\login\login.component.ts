import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthServiceService } from 'src/services/auth-service.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  email!: string;
  password!: string;
  name!: string;
  confirmpassword!: string; 


  isSignDivVisiable: boolean  = true;
  message: string = '';  // Ajout de la variable message


  constructor(private authService:AuthServiceService ,private router: Router){}

  onRegister() {
    if (this.password !== this.confirmpassword) {
      window.alert("Passwords don't match!");
      return;
    }
  
    // Appel de register avec des arguments séparés
    this.authService.register(this.name, this.email, this.password, this.confirmpassword).subscribe(
      (response) => {
        console.log('Registration successful', response);
        window.alert('Registration successful');
        this.isSignDivVisiable = false; // Passer à la page de login après inscription
      },
      (error) => {
        console.error('Registration failed', error);
        window.alert('Registration failed');
      }
    );
  }
  
  

  login() {
    this.authService.login(this.email, this.password).subscribe(response => {
      console.log(response);
  
      // Récupérer le token de la réponse
      const token = response.token;

  
      // Sauvegarder le token JWT dans le localStorage
      this.authService.setToken(token);
  
      // Récupérer le rôle de l'utilisateur depuis la réponse
      const user = response.user;

      this.authService.setUserName(user.name);
      this.authService.setUserRole(user.role); // Mettre à jour le rôle de l'utilisateur



          // Sauvegarde de l'ID de l'utilisateur dans le localStorage
    localStorage.setItem('user_id', user.id.toString());
  
      // Vérifier le rôle de l'utilisateur et rediriger en conséquence
      if (user.role === 'admin') {
        this.router.navigateByUrl('/Dashboard'); // Redirection pour l'admin
      } else if (user.role === 'client') {
        this.router.navigateByUrl('/home'); // Redirection pour le client
      }
  
    }, error => {
      window.alert('Login failed');
      console.error('Login error', error);
      // Handle login error
    });
  }
  
  
}


