import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AuthServiceService } from 'src/services/auth-service.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  email!: string;
  password!: string;
  name!: string;
  confirmpassword!: string;

  isSignDivVisiable: boolean = true;
  message: string = '';

  constructor(private authService: AuthServiceService, private router: Router) {}

  // Méthode d'inscription avec contraintes
  onRegister() {
    // Vérification des champs obligatoires
    if (!this.name || !this.email || !this.password || !this.confirmpassword) {
      window.alert('Veuillez remplir tous les champs.');
      return;
    }

    // Vérification format email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.email)) {
      window.alert('Veuillez entrer une adresse e-mail valide.');
      return;
    }

    // Vérification longueur du mot de passe
    if (this.password.length < 6) {
      window.alert('Le mot de passe doit contenir au moins 6 caractères.');
      return;
    }

    // Vérification confirmation mot de passe
    if (this.password !== this.confirmpassword) {
      window.alert('Les mots de passe ne correspondent pas.');
      return;
    }

    // Envoi de la requête
    this.authService.register(this.name, this.email, this.password, this.confirmpassword).subscribe(
      (response) => {
        console.log('Inscription réussie', response);
        window.alert('Inscription réussie');
        this.isSignDivVisiable = false; // Passer à la page de connexion
      },
      (error) => {
        console.error('Échec de l’inscription', error);
        window.alert('Échec de l’inscription');
      }
    );
  }

  // Méthode de connexion
  login() {
    this.authService.login(this.email, this.password).subscribe(
      response => {
        const token = response.token;
        this.authService.setToken(token);

        const user = response.user;
        this.authService.setUserName(user.name);
        this.authService.setUserRole(user.role);
        localStorage.setItem('user_id', user.id.toString());

        if (user.role === 'admin') {
          this.router.navigateByUrl('/Dashboard');
        } else if (user.role === 'client') {
          this.router.navigateByUrl('/home');
        }
      },
      error => {
        window.alert('Échec de la connexion');
        console.error('Erreur de connexion', error);
      }
    );
  }
}
