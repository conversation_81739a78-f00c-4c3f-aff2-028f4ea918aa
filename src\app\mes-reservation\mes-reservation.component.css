/* Structure de base */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8fafc;
}

.sidebar-fixed {
  width: 280px;
  min-height: 100vh;
  position: sticky;
  top: 0;
  background-color: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.main-content-wrapper {
  flex: 1;
  padding: 30px;
  overflow-x: auto;
}

/* Conteneur principal */
.reservations-container {
  max-width: 100%;
  margin: 0;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
}

/* En-tête */
.reservations-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: flex-end;
}

.reservations-title {
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reservations-title i {
  color: #3498db;
}

/* Barre de recherche */
.search-box {
  position: relative;
  width: 300px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.1rem;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f8fafc;
}

.search-box input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  background-color: white;
}

/* Légende des statuts */
.status-legend {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.legend-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
  margin-right: 8px;
}

.status-legend .status-badge {
  font-size: 0.75rem;
  padding: 4px 8px;
  min-width: auto;
}

/* Tableau */
.table-responsive {
  overflow-x: auto;
  margin-bottom: 30px;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.reservations-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
}

.table-header {
  background-color: #3498db;
  color: white;
  position: sticky;
  top: 0;
}

.table-header th {
  padding: 16px 12px;
  text-align: left;
  font-weight: 500;
  font-size: 0.95rem;
}

.reservation-row {
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s;
}

.reservation-row:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.reservation-row td {
  padding: 14px 12px;
  vertical-align: middle;
  font-size: 0.95rem;
}

/* Styles des cellules */
.client-cell .client-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
}

.client-email {
  font-size: 0.85rem;
  color: #7f8c8d;
}

.hotel-cell .hotel-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.hotel-name {
  font-weight: 500;
}

.hotel-location {
  font-size: 0.85rem;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 5px;
}

.hotel-location i {
  font-size: 0.8rem;
}

.room-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.room-type {
  font-weight: 500;
}

.room-price {
  font-size: 0.85rem;
  color: #28a745;
}

.date-cell .date-range {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-start, .date-end {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.date-start i, .date-end i {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Badge de statut */
.status-badge {
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: capitalize;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-width: 110px;
  text-align: center;
  justify-content: center;
  color: white;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-badge.confirmed {
  background-color: #28a745; /* Vert */
  color: white;
}

.status-badge.pending {
  background-color: #fd7e14; /* Orange */
  color: white;
}

.status-badge.cancelled {
  background-color: #dc3545; /* Rouge */
  color: white;
}

.status-badge i {
  font-size: 0.9rem;
}

/* Boutons d'action */
.actions-cell {
  min-width: 160px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
  font-size: 1rem;
}

.action-btn:hover {
  transform: scale(1.1);
  opacity: 0.9;
}

.confirm-btn {
  background-color: #28a745;
}

.cancel-btn {
  background-color: #dc3545;
}

.delete-btn {
  background-color: #6c757d;
}

/* Aucun résultat */
.no-results {
  text-align: center;
  padding: 30px;
  color: #6c757d;
}

.no-results i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #dee2e6;
}

.no-results p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 1200px) {
  .reservations-container {
    padding: 25px;
  }

  .reservations-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-box {
    width: 100%;
  }
}

@media (max-width: 992px) {
  .sidebar-fixed {
    width: 240px;
  }

  .main-content-wrapper {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }

  .sidebar-fixed {
    width: 100%;
    min-height: auto;
    position: relative;
  }

  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .reservations-table {
    min-width: 800px;
  }

  .action-buttons {
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .main-content-wrapper {
    padding: 15px;
  }

  .reservations-container {
    padding: 15px;
  }

  .reservations-title {
    font-size: 1.5rem;
  }

  .status-badge {
    padding: 6px 10px;
    font-size: 0.8rem;
    min-width: 80px;
  }
}