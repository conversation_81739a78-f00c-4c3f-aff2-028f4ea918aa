<div class="dashboard-layout">
  <!-- Sidebar fixe -->
  <app-sidebar class="sidebar-fixed"></app-sidebar>

  <!-- Contenu principal -->
  <div class="main-content-wrapper">
    <div class="reservations-container">
      <div class="reservations-header">
        <h2 class="reservations-title">
          <i class="fas fa-calendar-alt"></i> Gestion des Réservations
        </h2>

        <div class="search-box">
          <i class="fas fa-search"></i>
          <input type="text" placeholder="Rechercher une réservation..." [(ngModel)]="searchText">
        </div>
      </div>

      <div class="table-responsive">
        <table class="reservations-table">
          <thead>
            <tr class="table-header">
              <th class="client-column">Client</th>
              <th class="hotel-column">Établissement</th>
              <th class="room-column">Chambre</th>
              <th class="date-column">Dates</th>
              <th class="status-column">Statut</th>
              <th class="actions-column">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let reservation of filteredReservations()" class="reservation-row">
              <td class="client-cell">
                <div class="client-info">
                  <span class="client-name">{{ reservation.client.name }}</span>
                  <span class="client-email">{{ reservation.client.email }}</span>
                </div>
              </td>

              <td class="hotel-cell">
                <div class="hotel-info">
                  <span class="hotel-name">{{ reservation.chambre.hotel.nom }}</span>
                  <span class="hotel-location">
                    <i class="fas fa-map-marker-alt"></i> {{ reservation.chambre.hotel.ville }}
                  </span>
                </div>
              </td>

              <td class="room-cell">
                <span class="room-type">{{ reservation.chambre.type_chambre }}</span>
                <span class="room-price">{{ reservation.chambre.prix | currency:'EUR':'symbol':'1.2-2' }}/nuit</span>
              </td>

              <td class="date-cell">
                <div class="date-range">
                  <span class="date-start">
                    <i class="fas fa-sign-in-alt"></i> {{ reservation.date_debut | date:'dd/MM/yyyy' }}
                  </span>
                  <span class="date-end">
                    <i class="fas fa-sign-out-alt"></i> {{ reservation.date_fin | date:'dd/MM/yyyy' }}
                  </span>
                </div>
              </td>

              <td class="status-cell">
                <span class="status-badge"
                      [ngClass]="getStatusClass(reservation.status)"
                      [ngStyle]="getStatusStyle(reservation.status)">
                  <i [ngClass]="getStatusIcon(reservation.status)"></i>
                  {{ getStatusText(reservation.status) }}
                </span>
              </td>

              <td class="actions-cell">
                <div class="action-buttons">
                  <!-- Confirm Button -->
                  <button *ngIf="reservation.status !== 'confirmé'"
                          class="action-btn confirm-btn"
                          (click)="confirmReservation(reservation.id)"
                          title="Confirmer">
                    <i class="fas fa-check-circle"></i>
                  </button>

                  <!-- Cancel Button -->
                  <button *ngIf="reservation.status !== 'annulé'"
                          class="action-btn cancel-btn"
                          (click)="cancelReservation(reservation.id)"
                          title="Annuler">
                    <i class="fas fa-times-circle"></i>
                  </button>

                  <!-- Delete Button -->
                  <button class="action-btn delete-btn"
                          (click)="deleteRes(reservation.id)"
                          title="Supprimer">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </td>
            </tr>

            <tr *ngIf="filteredReservations().length === 0">
              <td colspan="6" class="no-results">
                <i class="fas fa-calendar-times"></i>
                <p>Aucune réservation trouvée</p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>