import { compileNgModule } from '@angular/compiler';
import { Component, OnInit } from '@angular/core';
import { ReservationService } from 'src/services/reservations.service';

@Component({
  selector: 'app-mes-reservation',
  templateUrl: './mes-reservation.component.html',
  styleUrls: ['./mes-reservation.component.css']
})
export class MesReservationComponent  implements OnInit {
  reservations: any[] = [];
  searchText: string = '';  // Variable pour la recherche


  constructor(private reservationService: ReservationService) { }

  filteredReservations() {
    if (!this.searchText) return this.reservations;
    return this.reservations.filter(res =>
      res.client.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      res.chambre.hotel.nom.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }


  ngOnInit(): void {
    this.getAllReservations();
  }

  getAllReservations(): void {
    this.reservationService.getAllReservations().subscribe(
      (data) => {
        this.reservations = data;
        console.log(data);
      },
      (error) => {
        console.error('Erreur lors de la récupération des réservations', error);
      }
    );
  }


  // Function to confirm a reservation
  confirmReservation(id: number): void {
    this.reservationService.confirmReservation(id).subscribe(
      (response) => {
        console.log('Réservation confirmée avec succès');
        this.getAllReservations(); // Reload the reservations after confirmation
      },
      (error) => {
        console.error('Erreur lors de la confirmation de la réservation', error);
      }
    );
  }

  // Function to cancel a reservation
  cancelReservation(id: number): void {
    this.reservationService.cancelReservation(id).subscribe(
      (response) => {
        console.log('Réservation annulée avec succès');
        this.getAllReservations(); // Reload the reservations after cancellation
      },
      (error) => {
        console.error('Erreur lors de l\'annulation de la réservation', error);
      }
    );
  }


    // Supprimer un res
  // Supprimer une réservation
deleteRes(id: number): void {
  if (confirm("Voulez-vous vraiment supprimer cette réservation ?")) {
    this.reservationService.deleteRes(id).subscribe({
      next: (response) => {
        alert("Réservation supprimée avec succès.");
        this.getAllReservations(); // Rafraîchir la liste
      },
      error: (err) => {
        console.error('Erreur lors de la suppression de la réservation :', err);
        alert("Une erreur est survenue lors de la suppression.");
      }
    });
  }
}

  // getStatusStyle(status: string): any {
  //   switch (status) {
  //     case 'confirmee':
  //       return { backgroundColor: '#458f45', color: 'white' };
  //     case 'en_attente':
  //       return { backgroundColor: '#9b9b9b', color: '#212529' };
  //     case 'annulee':
  //       return { backgroundColor: '#5c0000', color: 'white' };
  //     default:
  //       return { backgroundColor: '#5c0000', color: 'white' }; // gris pour statut inconnu
  //   }
  // }


  getStatusStyle(status: string): any {
    switch (status) {
      case 'confirmee':
        return { backgroundColor: '#28a745', color: 'white' }; // Vert
      case 'en_attente':
        return { backgroundColor: '#fd7e14', color: 'white' }; // Orange
      case 'annulee':
        return { backgroundColor: '#dc3545', color: 'white' }; // Rouge
      default:
        return { backgroundColor: '#6c757d', color: 'white' }; // Gris pour statut inconnu
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'confirmee':
        return 'Confirmé';
      case 'en_attente':
        return 'En attente';
      case 'annulee':
        return 'Annulé';
      default:
        return 'Inconnu';
    }
  }
}