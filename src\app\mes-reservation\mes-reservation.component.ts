import { compileNgModule } from '@angular/compiler';
import { Component, OnInit } from '@angular/core';
import { ReservationService } from 'src/services/reservations.service';

@Component({
  selector: 'app-mes-reservation',
  templateUrl: './mes-reservation.component.html',
  styleUrls: ['./mes-reservation.component.css']
})
export class MesReservationComponent  implements OnInit {
  reservations: any[] = [];
  searchText: string = '';  // Variable pour la recherche


  constructor(private reservationService: ReservationService) { }

  filteredReservations() {
    if (!this.searchText) return this.reservations;
    return this.reservations.filter(res =>
      res.client.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      res.chambre.hotel.nom.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }


  ngOnInit(): void {
    this.getAllReservations();
  }

  getAllReservations(): void {
    this.reservationService.getAllReservations().subscribe(
      (data) => {
        this.reservations = data;
        console.log(data);
      },
      (error) => {
        console.error('Erreur lors de la récupération des réservations', error);
      }
    );
  }


  // Function to confirm a reservation
  confirmReservation(id: number): void {
    this.reservationService.confirmReservation(id).subscribe(
      (response) => {
        console.log('Réservation confirmée avec succès');
        this.getAllReservations(); // Reload the reservations after confirmation
      },
      (error) => {
        console.error('Erreur lors de la confirmation de la réservation', error);
      }
    );
  }

  // Function to cancel a reservation
  cancelReservation(id: number): void {
    this.reservationService.cancelReservation(id).subscribe(
      (response) => {
        console.log('Réservation annulée avec succès');
        this.getAllReservations(); // Reload the reservations after cancellation
      },
      (error) => {
        console.error('Erreur lors de l\'annulation de la réservation', error);
      }
    );
  }


    // Supprimer un res
    deleteRes(id: number): void {
      if (confirm("Confirmer la suppression ?")) {
        this.reservationService.deleteRes(id).subscribe({
          next: () => {
            this.getAllReservations();
          },
          error: (err) => {
            console.error('Erreur lors de la suppression du client :', err);
          }
        });
      }

    }
  /**
   * Obtenir la classe CSS pour le statut
   */
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'confirmé':
      case 'confirmee':
        return 'status-confirmed';
      case 'en_attente':
      case 'en attente':
      case 'pending':
        return 'status-pending';
      case 'annulé':
      case 'annulee':
      case 'cancelled':
        return 'status-cancelled';
      case 'terminé':
      case 'terminee':
      case 'completed':
        return 'status-completed';
      default:
        return 'status-unknown';
    }
  }

  /**
   * Obtenir le style inline pour le statut (fallback)
   */
  getStatusStyle(status: string): any {
    switch (status.toLowerCase()) {
      case 'confirmé':
      case 'confirmee':
        return { backgroundColor: '#28a745', color: 'white' }; // Vert
      case 'en_attente':
      case 'en attente':
      case 'pending':
        return { backgroundColor: '#fd7e14', color: 'white' }; // Orange
      case 'annulé':
      case 'annulee':
      case 'cancelled':
        return { backgroundColor: '#dc3545', color: 'white' }; // Rouge
      case 'terminé':
      case 'terminee':
      case 'completed':
        return { backgroundColor: '#6c757d', color: 'white' }; // Gris
      default:
        return { backgroundColor: '#6c757d', color: 'white' }; // Gris pour statut inconnu
    }
  }

  /**
   * Obtenir l'icône pour le statut
   */
  getStatusIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'confirmé':
      case 'confirmee':
        return 'fas fa-check-circle';
      case 'en_attente':
      case 'en attente':
      case 'pending':
        return 'fas fa-clock';
      case 'annulé':
      case 'annulee':
      case 'cancelled':
        return 'fas fa-times-circle';
      case 'terminé':
      case 'terminee':
      case 'completed':
        return 'fas fa-flag-checkered';
      default:
        return 'fas fa-question-circle';
    }
  }

  /**
   * Obtenir le texte formaté pour le statut
   */
  getStatusText(status: string): string {
    switch (status.toLowerCase()) {
      case 'confirmé':
      case 'confirmee':
        return 'Confirmé';
      case 'en_attente':
      case 'en attente':
      case 'pending':
        return 'En attente';
      case 'annulé':
      case 'annulee':
      case 'cancelled':
        return 'Annulé';
      case 'terminé':
      case 'terminee':
      case 'completed':
        return 'Terminé';
      default:
        return 'Inconnu';
    }
  }
}