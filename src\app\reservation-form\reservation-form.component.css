.reservation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    background-color: #f5f7fa;
  }
  
  .reservation-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    padding: 32px;
    width: 100%;
    max-width: 500px;
  }
  
  .reservation-title {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 28px;
    font-size: 24px;
    font-weight: 600;
  }
  
  .reservation-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .form-label {
    color: #34495e;
    font-size: 14px;
    font-weight: 500;
  }
  
  .form-input {
    padding: 12px 16px;
    border: 1px solid #dfe6e9;
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.3s ease;
  }
  
  .form-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  }
  
  .submit-button {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 14px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    margin-top: 12px;
  }
  
  .submit-button:hover {
    background-color: #2ecc71;
    transform: translateY(-2px);
  }
  
  .submit-button:active {
    transform: translateY(0);
  }
  
  /* Pour l'icône Font Awesome */
  .button-icon {
    font-size: 16px;
  }
  
  /* Responsive */
  @media (max-width: 600px) {
    .reservation-card {
      padding: 24px;
    }
  }
  