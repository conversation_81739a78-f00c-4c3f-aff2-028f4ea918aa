<div class="reservation-container">
    <div class="reservation-card">
      <h2 class="reservation-title">Formulaire de Réservation</h2>
  
      <form (ngSubmit)="submitReservation()" class="reservation-form">
        <div class="form-group">
          <label for="dateDebut" class="form-label">Date de début</label>
          <input type="date"
                 class="form-input"
                 id="dateDebut"
                 name="date_debut"
                 [(ngModel)]="reservation.date_debut"
                 [min]="getCurrentDate()"
                 required
                 aria-label="Sélectionnez la date de début">
                 <!-- [(ngModel)]="reservation.date_debut" -->
        </div>
  
        <div class="form-group">
          <label for="dateFin" class="form-label">Date de fin</label>
          <input type="date"
                 class="form-input"
                 id="dateFin"
                 name="date_fin"
                 [(ngModel)]="reservation.date_fin"
                 [min]="reservation.date_debut"
                 required
                 aria-label="Sélectionnez la date de fin">
                 <!-- [(ngModel)]="reservation.date_fin" -->
        </div>
  
        <button type="submit" class="submit-button">
          <span class="button-text">Réserver</span>
          <i class="fas fa-calendar-check button-icon"></i>
        </button>
      </form>
    </div>
  </div>
  