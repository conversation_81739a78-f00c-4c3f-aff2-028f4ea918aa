/* Base Styles */
.hotel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  font-fa

/* Base Styles */
.hotel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
  font-family: 'Poppins', sans-serif;
}

/* Hero Section */
.hero-section {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.divider {
  width: 80px;
  height: 4px;
  background: #3498db;
  margin: 0 auto 1.5rem;
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  line-height: 1.6;
}

/* Special Offers Section */
.special-offers-section {
  margin: 4rem 0;
}

.gallery-container {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  gap: 2rem;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 1rem;
}

.grid-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.grid-item:hover {
  transform: translateY(-5px);
}

.grid-item.featured {
  grid-column: span 2;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-image:hover {
  transform: scale(1.05);
}

.offers-content {
  position: relative;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.offer-badge {
  position: absolute;
  top: -15px;
  right: 20px;
  background: #e74c3c;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.offers-title {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.offers-subtitle {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
  color: #34495e;
}

.check-icon {
  color: #27ae60;
  margin-right: 0.8rem;
  font-size: 1.2rem;
}

.cta-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

/* Room Filter Tabs */
.room-filter-tabs {
  margin: 3rem 0;
}

.tabs-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.filter-tab {
  padding: 0.8rem 1.5rem;
  background: #f8f9fa;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

.filter-tab:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.filter-tab.active {
  background: #3498db;
  color: white;
}

/* Room Cards */
.room-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.room-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.room-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.12);
}

.room-gallery {
  position: relative;
}

.main-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
}

.thumbnail-container {
  display: flex;
  padding: 0.5rem;
  background: #f8f9fa;
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 0.5rem;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #3498db;
}

.room-details {
  padding: 1.5rem;
}

.room-type {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.room-specs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.8rem;
  margin-bottom: 1.5rem;
  color: #7f8c8d;
}

.room-specs i {
  margin-right: 0.5rem;
  color: #3498db;
}

.price-section {
  margin: 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.price {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.per-night {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.hotel-info {
  margin-bottom: 1.5rem;
}

.hotel-name {
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.hotel-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.8rem;
}

.location-rating {
  display: flex;
  justify-content: space-between;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.rating-stars {
  color: #f39c12;
}

.book-button {
  width: 100%;
  padding: 0.8rem;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.book-button:hover {
  background: #2ecc71;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
}

/* Responsive Design */
@media (max-width: 992px) {
  .gallery-container {
    grid-template-columns: 1fr;
  }

  .room-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .tabs-container {
    flex-wrap: wrap;
  }

  .filter-tab {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .room-specs {
    grid-template-columns: 1fr;
  }
}
/* Filtres */
.filters-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.availability-filter, .type-filter {
  flex: 1;
  min-width: 250px;
}

.availability-filter label, .type-filter label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.availability-options, .type-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.availability-options button, .type-options button {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.availability-options button.active, .type-options button.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.availability-options button:hover, .type-options button:hover {
  border-color: #3498db;
}

.available-icon {
  color: #27ae60;
  margin-right: 5px;
}

.unavailable-icon {
  color: #e74c3c;
  margin-right: 5px;
}

/* No rooms message */
.no-rooms {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-rooms i {
  font-size: 50px;
  margin-bottom: 20px;
  color: #bdc3c7;
}

.no-rooms p {
  font-size: 18px;
}
/* Styles généraux */
.hotel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
   url('https://example.com/hotel-bg.jpg');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 80px 20px;
  text-align: center;
  border-radius: 10px;
  margin-bottom: 40px;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.divider {
  width: 80px;
  height: 3px;
  background: #f39c12;
  margin: 0 auto 20px;
}

.hero-subtitle {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
}

/* Special Offers */
.special-offers-section {
  margin-bottom: 40px;
}

.offer-container {
  display: flex;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.offer-image {
  flex: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.offer-content {
  flex: 1;
  padding: 30px;
  position: relative;
}

.offer-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.offer-badge {
  background: #e74c3c;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.offer-remaining {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.offer-title {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 15px;
}

.offer-description {
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 25px;
}

.discount-banner {
  background: #f1c40f;
  color: #2c3e50;
  padding: 15px;
  text-align: center;
  border-radius: 5px;
  margin: 25px 0;
  font-weight: bold;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.benefit-item {
  margin-bottom: 10px;
  color: #34495e;
}

.benefit-item i {
  margin-right: 10px;
  color: #3498db;
}

.cta-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s;
}

.cta-button:hover {
  background: #2980b9;
}

/* Filter Section - Version améliorée */
.filter-section {
  margin: 40px 0 30px;
}

.filter-title {
  color: #2d3436;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 600;
  text-align: center;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.filter-tab {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #f5f6fa;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #dfe6e9;
  font-size: 0.95rem;
  color: #636e72;
}

.filter-tab i {
  margin-right: 8px;
  font-size: 1rem;
  color: #74b9ff;
}

.filter-tab:hover {
  background: #dfe6e9;
  color: #2d3436;
}

.filter-tab.active {
  background: #0984e3;
  color: white;
  border-color: #0984e3;
  box-shadow: 0 4px 12px rgba(9, 132, 227, 0.2);
}

.filter-tab.active i {
  color: white;
}

/* Room Cards */
.room-cards-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.room-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.room-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.card-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.room-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.room-card:hover .room-main-image {
  transform: scale(1.05);
}

.room-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #ff4757;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.card-content {
  padding: 20px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.room-title {
  font-size: 1.4rem;
  color: #2f3542;
  margin: 0;
}

.room-rating {
  color: #ffa502;
}

.room-features {
  margin: 20px 0;
}

.feature {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #57606f;
}

.feature i {
  margin-right: 10px;
  color: #747d8c;
  width: 20px;
  text-align: center;
}

.price-section {
  margin: 25px 0;
}

.original-price {
  text-decoration: line-through;
  color: #747d8c;
  font-size: 0.9rem;
}

.discounted-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2f3542;
}

.per-night {
  font-size: 1rem;
  font-weight: normal;
  color: #57606f;
}

.tax-info {
  font-size: 0.8rem;
  color: #747d8c;
  margin-top: 5px;
}

.book-button {
  width: 100%;
  padding: 12px;
  background: #1e90ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.book-button:hover {
  background: #3742fa;
}

/* No rooms available */
.no-rooms {
  text-align: center;
  padding: 50px;
  color: #57606f;
  grid-column: 1 / -1;
}

.no-rooms i {
  font-size: 3rem;
  color: #a4b0be;
  margin-bottom: 20px;
}

.no-rooms p {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .offer-container {
    flex-direction: column;
  }

  .filter-tabs {
    gap: 8px;
  }

  .filter-tab {
    padding: 10px 15px;
    font-size: 0.85rem;
  }

  .room-cards-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 40px) {
  .hero-section {
    padding: 5px 5px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}


mily: 'Poppins', sans-serif;
}

/* Hero Section */
.hero-section {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.divider {
  width: 80px;
  height: 4px;
  background: #3498db;
  margin: 0 auto 1.5rem;
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.1rem;
  color: #7f8c8d;
  line-height: 1.6;
}

/* Special Offers Section */
.special-offers-section {
  margin: 4rem 0;
}

.gallery-container {
  display: grid;
  grid-template-columns: 1.5fr 1fr;
  gap: 2rem;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 1rem;
}

.grid-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.grid-item:hover {
  transform: translateY(-5px);
}

.grid-item.featured {
  grid-column: span 2;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-image:hover {
  transform: scale(1.05);
}

.offers-content {
  position: relative;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.offer-badge {
  position: absolute;
  top: -15px;
  right: 20px;
  background: #e74c3c;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.offers-title {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.offers-subtitle {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
  color: #34495e;
}

.check-icon {
  color: #27ae60;
  margin-right: 0.8rem;
  font-size: 1.2rem;
}

.cta-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
}

/* Room Filter Tabs */
.room-filter-tabs {
  margin: 3rem 0;
}

.tabs-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.filter-tab {
  padding: 0.8rem 1.5rem;
  background: #f8f9fa;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

.filter-tab:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.filter-tab.active {
  background: #3498db;
  color: white;
}

/* Room Cards */
.room-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.room-card {
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.room-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.12);
}

.room-gallery {
  position: relative;
}

.main-image {
  width: 100%;
  height: 220px;
  object-fit: cover;
}

.thumbnail-container {
  display: flex;
  padding: 0.5rem;
  background: #f8f9fa;
}

.thumbnail {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 0.5rem;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.thumbnail:hover {
  border-color: #3498db;
}

.room-details {
  padding: 1.5rem;
}

.room-type {
  font-size: 1.4rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.room-specs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.8rem;
  margin-bottom: 1.5rem;
  color: #7f8c8d;
}

.room-specs i {
  margin-right: 0.5rem;
  color: #3498db;
}

.price-section {
  margin: 1.5rem 0;
  padding: 1rem 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.price {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
}

.per-night {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.hotel-info {
  margin-bottom: 1.5rem;
}

.hotel-name {
  font-size: 1.1rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.hotel-description {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.8rem;
}

.location-rating {
  display: flex;
  justify-content: space-between;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.rating-stars {
  color: #f39c12;
}

.book-button {
  width: 100%;
  padding: 0.8rem;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.book-button:hover {
  background: #2ecc71;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 204, 113, 0.3);
}

/* Responsive Design */
@media (max-width: 992px) {
  .gallery-container {
    grid-template-columns: 1fr;
  }

  .room-cards-container {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .tabs-container {
    flex-wrap: wrap;
  }

  .filter-tab {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .room-specs {
    grid-template-columns: 1fr;
  }
}
/* Filtres */
.filters-container {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.availability-filter, .type-filter {
  flex: 1;
  min-width: 250px;
}

.availability-filter label, .type-filter label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: #2c3e50;
}

.availability-options, .type-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.availability-options button, .type-options button {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.availability-options button.active, .type-options button.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.availability-options button:hover, .type-options button:hover {
  border-color: #3498db;
}

.available-icon {
  color: #27ae60;
  margin-right: 5px;
}

.unavailable-icon {
  color: #e74c3c;
  margin-right: 5px;
}

/* No rooms message */
.no-rooms {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-rooms i {
  font-size: 50px;
  margin-bottom: 20px;
  color: #bdc3c7;
}

.no-rooms p {
  font-size: 18px;
}
/* Styles généraux */
.hotel-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
   url('https://example.com/hotel-bg.jpg');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 80px 20px;
  text-align: center;
  border-radius: 10px;
  margin-bottom: 40px;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.divider {
  width: 80px;
  height: 3px;
  background: #f39c12;
  margin: 0 auto 20px;
}

.hero-subtitle {
  font-size: 1.2rem;
  max-width: 700px;
  margin: 0 auto;
}

/* Special Offers */
.special-offers-section {
  margin-bottom: 40px;
}

.offer-container {
  display: flex;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.offer-image {
  flex: 1;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.offer-content {
  flex: 1;
  padding: 30px;
  position: relative;
}

.offer-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.offer-badge {
  background: #e74c3c;
  color: white;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.offer-remaining {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.offer-title {
  font-size: 1.8rem;
  color: #2c3e50;
  margin-bottom: 15px;
}

.offer-description {
  color: #34495e;
  line-height: 1.6;
  margin-bottom: 25px;
}

.discount-banner {
  background: #f1c40f;
  color: #2c3e50;
  padding: 15px;
  text-align: center;
  border-radius: 5px;
  margin: 25px 0;
  font-weight: bold;
}

.benefits-list {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.benefit-item {
  margin-bottom: 10px;
  color: #34495e;
}

.benefit-item i {
  margin-right: 10px;
  color: #3498db;
}

.cta-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 5px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s;
}

.cta-button:hover {
  background: #2980b9;
}

/* Filter Section - Version améliorée */
.filter-section {
  margin: 40px 0 30px;
}

.filter-title {
  color: #2d3436;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 600;
  text-align: center;
}

.filter-tabs {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.filter-tab {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #f5f6fa;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #dfe6e9;
  font-size: 0.95rem;
  color: #636e72;
}

.filter-tab i {
  margin-right: 8px;
  font-size: 1rem;
  color: #74b9ff;
}

.filter-tab:hover {
  background: #dfe6e9;
  color: #2d3436;
}

.filter-tab.active {
  background: #0984e3;
  color: white;
  border-color: #0984e3;
  box-shadow: 0 4px 12px rgba(9, 132, 227, 0.2);
}

.filter-tab.active i {
  color: white;
}

/* Room Cards */
.room-cards-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 20px;
}

.room-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.room-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.card-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.room-main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.room-card:hover .room-main-image {
  transform: scale(1.05);
}

.room-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #ff4757;
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
}

.card-content {
  padding: 20px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.room-title {
  font-size: 1.4rem;
  color: #2f3542;
  margin: 0;
}

.room-rating {
  color: #ffa502;
}

.room-features {
  margin: 20px 0;
}

.feature {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  color: #57606f;
}

.feature i {
  margin-right: 10px;
  color: #747d8c;
  width: 20px;
  text-align: center;
}

.price-section {
  margin: 25px 0;
}

.original-price {
  text-decoration: line-through;
  color: #747d8c;
  font-size: 0.9rem;
}

.discounted-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2f3542;
}

.per-night {
  font-size: 1rem;
  font-weight: normal;
  color: #57606f;
}

.tax-info {
  font-size: 0.8rem;
  color: #747d8c;
  margin-top: 5px;
}

.book-button {
  width: 100%;
  padding: 12px;
  background: #1e90ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.book-button:hover {
  background: #3742fa;
}

/* No rooms available */
.no-rooms {
  text-align: center;
  padding: 50px;
  color: #57606f;
  grid-column: 1 / -1;
}

.no-rooms i {
  font-size: 3rem;
  color: #a4b0be;
  margin-bottom: 20px;
}

.no-rooms p {
  font-size: 1.2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .offer-container {
    flex-direction: column;
  }

  .filter-tabs {
    gap: 8px;
  }

  .filter-tab {
    padding: 10px 15px;
    font-size: 0.85rem;
  }

  .room-cards-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 40px) {
  .hero-section {
    padding: 5px 5px;
  }

  .hero-title {
    font-size: 1.8rem;
  }

  .filter-tab {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

/* Styles pour la modale de réservation améliorée */
.modal-content {
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  border-radius: 15px 15px 0 0;
}

.reservation-form .form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.reservation-form .form-control {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 12px 15px;
  transition: all 0.3s ease;
}

.reservation-form .form-control:focus {
  border-color: #17a2b8;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

.modal-footer .btn {
  border-radius: 25px;
  padding: 10px 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.modal-footer .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Styles pour le résumé de réservation */
.reservation-summary {
  margin: 20px 0;
}

.summary-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.summary-title {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

.total-row {
  background-color: rgba(23, 162, 184, 0.1);
  border-radius: 8px;
  padding: 12px 15px;
  margin-top: 10px;
  border: 2px solid rgba(23, 162, 184, 0.2);
}

.total-row span {
  font-size: 1.1rem;
  font-weight: 700;
}

/* Styles pour la vérification de disponibilité */
.availability-check {
  margin: 15px 0;
}

.availability-check .alert {
  border-radius: 10px;
  padding: 15px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.availability-check .alert i {
  font-size: 1.2rem;
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-color: rgba(40, 167, 69, 0.3);
  color: #155724;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
  color: #856404;
}

/* Responsive design pour le formulaire */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 10px;
  }

  .summary-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .modal-footer .btn {
    width: 100%;
  }
}