
<app-header></app-header>

<div class="hotel-container">


  <!-- Special Offers section -->
<!-- Special Offers section -->
<section class="special-offers-section">
  <div class="gallery-container">
    <div class="grid-item">
      <img [src]="offres[0]?.imageUrl || 'https://sotufab.tn/wp-content/uploads/2024/03/clara_Plan-de-travail-1.jpg'"
           alt="Hotel room"
           class="gallery-image">
    </div>
    <div class="offers-content" *ngIf="offres.length > 0">
      <div class="offer-badge">Limited Offer</div>
      <h3 class="offers-title">{{ offres[0].titre }}</h3>
      <p class="offers-subtitle">
        {{ offres[0].description }}
      </p>

      <div class="discount-banner">
        <span class="discount-value">-{{ offres[0].valeur_remise }}%</span>
      </div>

      <ul class="benefits-list">
        <li class="benefit-item">
          <span class="check-icon"><i class="fas fa-check-circle"></i></span>
          <span>Free Wi-Fi Service</span>
        </li>
        <li class="benefit-item">
          <span class="check-icon"><i class="fas fa-check-circle"></i></span>
          <span>Best Rate Guarantee</span>
        </li>
        <li class="benefit-item">
          <span class="check-icon"><i class="fas fa-check-circle"></i></span>
          <span>Free DSTV Access</span>
        </li>
      </ul>

      <div class="offer-remaining" *ngIf="offres[0].date_fin_promo">
        <i class="far fa-clock"></i> Valid until {{ offres[0].date_fin_promo | date:'dd/MM/yyyy' }}
      </div>

      <!-- <button class="cta-button">
        View More <i class="fas fa-arrow-right"></i>
      </button> -->
    </div>
  </div>
</section>


  <!-- Room Filter Tabs - Version améliorée -->
  <div class="filter-section">
    <h3 class="filter-title">Filtrer par type de chambre</h3>
    <div class="filter-tabs">
      <div *ngFor="let type of chambreTypes"
           (click)="onChangeType(type)"
           class="filter-tab"
           [class.active]="selectedType === type">
        <i [class]="getRoomTypeIcon(type)"></i>
        <span>{{ type | titlecase }}</span>
      </div>
    </div>
  </div>

  <!-- Room Cards -->
  <div class="room-cards-section">
    <div *ngFor="let chambre of filteredChambres" class="room-card">
      <div class="card-image">
        <img [src]="chambre.image_chambre || 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRCiwmaR1G66dnnoeuRbDhvTN7ae_tHaNHZ4vSwkSDchg&s&ec=72940545'"
             [alt]="'Chambre ' + chambre.type_chambre"
             class="room-main-image">

        <div class="room-badge" *ngIf="chambre.prix_avec_remise < chambre.prixparnuit">
          Promotion
        </div>
      </div>

      <div class="card-content">
        <div class="room-header">
          <h3 class="room-title">Chambre {{ chambre.type_chambre | titlecase }}</h3>
          <!-- <div class="room-rating">
            <i *ngFor="let star of getStars(chambre.hotel?.nbre_etoiles || 3)"
               class="fas fa-star"></i>
          </div> -->
        </div>

        <div class="room-features">
          <div class="feature">
            <i class="fas fa-door-open"></i>
            <span>Numéro: {{ chambre.numero }}</span>
          </div>

        </div>

        <div class="price-section">
          <div class="original-price" *ngIf="chambre.prix_avec_remise < chambre.prixparnuit">
            {{ chambre.prixparnuit }} DT
          </div>
          <div class="discounted-price">
            {{ chambre.prix_avec_remise }} DT <span class="per-night">/ nuit</span>
          </div>
          <div class="tax-info">TVA incluse</div>
        </div>

        <!-- <button class="book-button"
                [routerLink]="['/formulairereservation']"
                [queryParams]="{ chambreId: chambre.id }">
          <i class="fas fa-calendar-check"></i> Réserver
        </button> -->

        <button class="btn btn-outline-primary" (click)="openModal(chambre.id)">
          <i class="fas fa-calendar-check"></i> Réserver
        </button>



      </div>
    </div>
  </div>

  <div class="no-rooms" *ngIf="filteredChambres.length === 0">
    <i class="fas fa-bed"></i>
    <p>Aucune chambre disponible pour cette catégorie</p>
  </div>
  <!-- Modal de réservation -->
<!-- Modal Bootstrap pour la réservation -->
<div class="modal fade" id="reservationModal" tabindex="-1" aria-labelledby="reservationModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content rounded-3 shadow">
      <div class="modal-header bg-info text-white">
        <h5 class="modal-title" id="reservationModalLabel">Formulaire de Réservation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
      </div>
      <div class="modal-body">
        <form (ngSubmit)="submitReservation()" class="reservation-form">
          <!-- Informations de base -->
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="dateDebut" class="form-label">Date d'arrivée *</label>
              <input type="date" id="dateDebut"
                     class="form-control"
                     [(ngModel)]="reservation.date_debut"
                     name="date_debut" required
                     [min]="getCurrentDate()"
                     (change)="onDateChange()">
            </div>

            <div class="col-md-6 mb-3">
              <label for="dateFin" class="form-label">Date de départ *</label>
              <input type="date" id="dateFin"
                     class="form-control"
                     [(ngModel)]="reservation.date_fin"
                     name="date_fin"
                     [min]="getMinEndDate()"
                     required
                     (change)="onDateChange()">
            </div>
          </div>

          <!-- Informations sur les personnes -->
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="nombrePersonnes" class="form-label">Nombre de personnes</label>
              <select id="nombrePersonnes" class="form-control"
                      [(ngModel)]="reservation.nombre_personnes"
                      name="nombre_personnes"
                      (change)="onDateChange()">
                <option value="1">1 personne</option>
                <option value="2">2 personnes</option>
                <option value="3">3 personnes</option>
                <option value="4">4 personnes</option>
                <option value="5">5+ personnes</option>
              </select>
            </div>

            <div class="col-md-6 mb-3">
              <label for="telephone" class="form-label">Téléphone</label>
              <input type="tel" id="telephone"
                     class="form-control"
                     [(ngModel)]="reservation.telephone"
                     name="telephone"
                     placeholder="+33 6 12 34 56 78">
            </div>
          </div>

          <!-- Informations de calcul -->
          <div class="reservation-summary" *ngIf="reservationSummary">
            <div class="summary-card">
              <h6 class="summary-title">
                <i class="fas fa-calculator me-2"></i>Résumé de la réservation
              </h6>
              <div class="summary-details">
                <div class="summary-row">
                  <span>Nombre de nuits :</span>
                  <span class="fw-bold">{{ reservationSummary.nombreNuits }}</span>
                </div>
                <div class="summary-row">
                  <span>Prix par nuit :</span>
                  <span class="fw-bold">{{ reservationSummary.prixParNuit | currency:'EUR':'symbol':'1.2-2' }}</span>
                </div>
                <div class="summary-row total-row">
                  <span>Total :</span>
                  <span class="fw-bold text-primary">{{ reservationSummary.prixTotal | currency:'EUR':'symbol':'1.2-2' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Demandes spéciales -->
          <div class="mb-3">
            <label for="demandesSpeciales" class="form-label">Demandes spéciales</label>
            <textarea id="demandesSpeciales"
                      class="form-control"
                      [(ngModel)]="reservation.demandes_speciales"
                      name="demandes_speciales"
                      rows="3"
                      placeholder="Lit bébé, vue sur mer, étage élevé, etc."></textarea>
          </div>

          <!-- Mode de paiement -->
          <div class="mb-3">
            <label for="modePaiement" class="form-label">Mode de paiement préféré</label>
            <select id="modePaiement" class="form-control"
                    [(ngModel)]="reservation.mode_paiement"
                    name="mode_paiement">
              <option value="carte">Carte bancaire</option>
              <option value="especes">Espèces</option>
              <option value="virement">Virement bancaire</option>
              <option value="cheque">Chèque</option>
            </select>
          </div>

          <!-- Vérification de disponibilité -->
          <div class="availability-check" *ngIf="availabilityStatus">
            <div class="alert" [ngClass]="{
              'alert-success': availabilityStatus.available,
              'alert-warning': !availabilityStatus.available
            }">
              <i class="fas" [ngClass]="{
                'fa-check-circle': availabilityStatus.available,
                'fa-exclamation-triangle': !availabilityStatus.available
              }"></i>
              {{ availabilityStatus.message }}
            </div>
          </div>

          <div class="modal-footer p-0 pt-3">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="button" class="btn btn-info me-2" (click)="checkAvailability()"
                    [disabled]="!reservation.date_debut || !reservation.date_fin">
              <i class="fas fa-search me-1"></i>Vérifier disponibilité
            </button>
            <button type="submit" class="btn btn-primary"
                    [disabled]="!availabilityStatus?.available">
              <i class="fas fa-calendar-check me-1"></i>Réserver
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>


</div>
