import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Reservation } from 'src/models/Reservation.model';
import { ReservationService } from 'src/services/reservations.service';
import { AuthServiceService } from 'src/services/auth-service.service';
import { RoomsService } from 'src/services/rooms.service';
import Swal from 'sweetalert2';
declare var bootstrap: any;
@Component({
  selector: 'app-room',
  templateUrl: './room.component.html',
  styleUrls: ['./room.component.css']
})
export class RoomComponent implements OnInit {
  chambres: any[] = [];
  offres: any[] = [];
  filteredChambres: any[] = [];
  chambreTypes: string[] = [];
  selectedType: string = '';
  selectedAvailability: boolean | null = null;
  IsLoggedIn(): boolean {
  return !!localStorage.getItem('token'); // ou selon ta logique de stockage
  }


  hotelId!: number;
  hotelName: string = '';
  hotelOffers: string = '';

  showModal: boolean = false;
  selectedChambreId: number | null = null;

  reservation: Reservation = {
    client_id: 0,
    chambre_id: 0,
    date_debut: new Date(),
    date_fin: new Date(),
    status: 'en_attente'
  };

  constructor(
    private route: ActivatedRoute,
    private RoomService: RoomsService,
    private reservationService: ReservationService,
    private authService: AuthServiceService
  ) {}

  
  
  ngOnInit(): void {
    const idParam = this.route.snapshot.paramMap.get('id');
    if (idParam) {
      this.hotelId = +idParam;
      this.loadHotelRooms();
    } else {
      console.error("Aucun ID d’hôtel fourni dans l’URL.");
    }
  }

  loadHotelRooms() {
    this.RoomService.getHotelWithRooms(this.hotelId).subscribe({
      next: (response) => {
        if (response.success && response.hotel) {
          this.hotelName = response.hotel.nom;
          this.chambres = response.hotel.chambres;
          this.offres = response.hotel.offre ? [response.hotel.offre] : [];

          this.chambreTypes = [...new Set(this.chambres.map(c => c.type_chambre))];
          this.selectedType = this.chambreTypes[0];
          this.filterChambres();
        }
      },
      error: (err) => {
        console.error('Erreur de chargement des chambres de l’hôtel', err);
      }
    });
  }

  onChangeType(type: string): void {
    this.selectedType = type;
    this.filterChambres();
  }

  onChangeAvailability(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const value = selectElement.value;
    this.selectedAvailability = value !== '' ? Boolean(parseInt(value)) : null;
    this.filterChambres();
  }

  filterChambres(): void {
    this.filteredChambres = this.chambres.filter(c => {
      const typeMatch = this.selectedType ? c.type_chambre === this.selectedType : true;
      const availabilityMatch = this.selectedAvailability !== null
        ? c.disponibilite === this.selectedAvailability
        : true;

      return typeMatch && availabilityMatch;
    });
  }

  getStars(count: number): any[] {
    return Array(count).fill(0);
  }

  getRoomTypeIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'standard': 'fas fa-bed',
      'deluxe': 'fas fa-bed fa-lg',
      'suite': 'fas fa-couch',
      'familiale': 'fas fa-home',
      'executive': 'fas fa-star'
    };
    return icons[type.toLowerCase()] || 'fas fa-bed';
  }

  openModal(chambreId: number): void {
    this.selectedChambreId = chambreId;
    this.reservation.chambre_id = chambreId;

    const clientId = this.authService.getUserId();
    if (clientId) {
      this.reservation.client_id = parseInt(clientId);
    }

    const modalElement = document.getElementById('reservationModal');
    if (modalElement) {
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  closeModal(): void {
    this.showModal = false;
    this.selectedChambreId = null;
  }

  getCurrentDate(): string {
    return new Date().toISOString().split('T')[0];
  }

  submitReservation() {
    if (new Date(this.reservation.date_fin) <= new Date(this.reservation.date_debut)) {
       Swal.fire("La date de fin doit être après la date de début!");
      return;
    }

    this.reservationService.createReservation(this.reservation).subscribe({
      next: (res) => {
        Swal.fire({
          title: "Bien",
          text: "Réservation effectuée avec succès !",
          icon: "success"
        });

        console.log(res);
        this.closeModal(); // Fermer la modale après soumission
      },
      
      error: (err) => {
        console.error('Erreur lors de la réservation :', err);
        Swal.fire({
          icon: "error",
          title: "Oops...",
          text:err.error?.message || "'Erreur lors de la réservation.'",
          footer: !this.IsLoggedIn ? '<a href="/register">Il faut s’authentifier</a>' : ''
        });
      }
    });
  }
}
