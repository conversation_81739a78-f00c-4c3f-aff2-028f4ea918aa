/* Styles de base pour le badge de statut */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: statusAppear 0.3s ease-in-out;
}

.status-badge i {
  font-size: 0.9rem;
}

/* Tailles du badge */
.status-badge-small {
  font-size: 0.75rem;
  padding: 4px 8px;
  gap: 4px;
}

.status-badge-small i {
  font-size: 0.8rem;
}

.status-badge-medium {
  font-size: 0.85rem;
  padding: 6px 12px;
  gap: 6px;
}

.status-badge-medium i {
  font-size: 0.9rem;
}

.status-badge-large {
  font-size: 1rem;
  padding: 8px 16px;
  gap: 8px;
}

.status-badge-large i {
  font-size: 1.1rem;
}

/* Statut Confirmé - VERT */
.status-confirmed {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: 2px solid #1e7e34;
}

.status-confirmed:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Statut En attente - ORANGE */
.status-pending {
  background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
  color: white;
  border: 2px solid #e55a00;
}

.status-pending:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(253, 126, 20, 0.3);
}

/* Statut Annulé - ROUGE */
.status-cancelled {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
  color: white;
  border: 2px solid #bd2130;
}

.status-cancelled:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

/* Statut Terminé - GRIS */
.status-completed {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border: 2px solid #545b62;
}

.status-completed:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Statut Inconnu - GRIS FONCÉ */
.status-unknown {
  background: linear-gradient(135deg, #6c757d 0%, #343a40 100%);
  color: white;
  border: 2px solid #495057;
}

/* Animation d'apparition */
@keyframes statusAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}