import { Component, Input } from '@angular/core';
import { StatusService } from '../../../services/status.service';

@Component({
  selector: 'app-status-badge',
  templateUrl: './status-badge.component.html',
  styleUrls: ['./status-badge.component.css']
})
export class StatusBadgeComponent {
  @Input() status: string = '';
  @Input() showIcon: boolean = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';

  constructor(private statusService: StatusService) { }

  get statusClass(): string {
    return this.statusService.getStatusClass(this.status);
  }

  get statusStyle(): any {
    return this.statusService.getStatusStyle(this.status);
  }

  get statusIcon(): string {
    return this.statusService.getStatusIcon(this.status);
  }

  get statusText(): string {
    return this.statusService.getStatusText(this.status);
  }

  get sizeClass(): string {
    return `status-badge-${this.size}`;
  }
}
