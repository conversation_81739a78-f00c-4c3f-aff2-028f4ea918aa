/* Enhanced Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-lighter);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }

.sidebar {
    width: 250px;
    background-color: var(--white);
    box-shadow: var(--shadow-md);
    padding: 2rem 1.5rem;
    transition: var(--transition);
    z-index: 10;
  }
  
  .main-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
  }
  
  /* Typography */
  .sidebar-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--bg-light);
  }
  /* Navigation */
.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    color: var(--text-medium);
    font-weight: 500;
    transition: var(--transition);
    text-decoration: none;
  }
  
  .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(79, 70, 229, 0.1);
  }
  
  .nav-link.active {
    color: var(--white);
    background-color: var(--primary-color);
    font-weight: 600;
  }
  @media (max-width: 768px) {
    .dashboard-container {
      flex-direction: column;
    }
    
    .sidebar {
      width: 100%;
      height: auto;
      position: relative;
      padding: 1.5rem;
    }
    
    .main-content {
      padding: 1.5rem;
    }
    
    .sidebar-nav {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0.5rem;
    }
    
    .nav-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
    
    .nav-link:hover {
      transform: translateY(2px);
    }
  }