/* Nouvelle structure de layout */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.sidebar-fixed {
  width: 280px;
  min-height: 100vh;
  position: sticky;
  top: 0;
  background-color: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.main-content-wrapper {
  flex: 1;
  padding: 30px;
  overflow-x: auto;
}

/* Améliorations du conteneur clients */
.clients-container {
  max-width: 100%;
  margin: 0;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
}

.clients-title {
  color: #2c3e50;
  margin-bottom: 25px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #3498db;
  font-size: 1.8rem;
}

/* Amélioration de la barre de recherche */
.search-box {
  position: relative;
  width: 350px;
  margin-bottom: 25px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
  font-size: 1.1rem;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 1px solid #e0e6ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f8fafc;
}

.search-box input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  background-color: white;
}

/* Amélioration du tableau */
.clients-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
}

.table-header {
  background-color: #3498db;
  color: white;
  position: sticky;
  top: 0;
}

.table-header th {
  padding: 18px 15px;
  text-align: left;
  font-weight: 500;
  font-size: 0.95rem;
}

.client-row {
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s;
}

.client-row:last-child {
  border-bottom: none;
}

.client-row:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.client-row td {
  padding: 15px;
  vertical-align: middle;
  font-size: 0.95rem;
}

/* Amélioration des actions */
.actions-cell {
  display: flex;
  gap: 15px;
}

.icon-action {
  font-size: 1.3rem;
  cursor: pointer;
  transition: all 0.2s;
  padding: 8px;
  border-radius: 50%;
}

.fa-edit {
  color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
}

.fa-trash-alt {
  color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
}

.icon-action:hover {
  transform: scale(1.1);
  opacity: 1;
}

.fa-edit:hover {
  background-color: rgba(52, 152, 219, 0.2);
}

.fa-trash-alt:hover {
  background-color: rgba(231, 76, 60, 0.2);
}

/* Avatar amélioré */
.rounded-circle {
  border: 2px solid #e0e6ed;
  transition: all 0.3s;
  object-fit: cover;
}

.client-row:hover .rounded-circle {
  border-color: #3498db;
  transform: scale(1.05);
}

/* Responsive */
@media (max-width: 992px) {
  .sidebar-fixed {
    width: 240px;
  }
  
  .main-content-wrapper {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-layout {
    flex-direction: column;
  }
  
  .sidebar-fixed {
    width: 100%;
    min-height: auto;
    position: relative;
  }
  
  .clients-container {
    padding: 20px;
  }
  
  .search-box {
    width: 100%;
  }
  
  .table-header th, .client-row td {
    padding: 12px 8px;
  }
  
  .actions-cell {
    gap: 10px;
  }
  
  .icon-action {
    font-size: 1.1rem;
    padding: 6px;
  }
}

@media (max-width: 576px) {
  .main-content-wrapper {
    padding: 15px;
  }
  
  .clients-container {
    padding: 15px;
  }
  
  .clients-title {
    font-size: 1.5rem;
  }
  
  .table-header th {
    font-size: 0.85rem;
    padding: 10px 5px;
  }
  
  .client-row td {
    font-size: 0.85rem;
    padding: 10px 5px;
  }
}