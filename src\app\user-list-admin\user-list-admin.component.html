<div class="dashboard-layout">
  <!-- Sidebar fixe -->
  <app-sidebar class="sidebar-fixed"></app-sidebar>

  <!-- Contenu principal -->
  <div class="main-content-wrapper">
    <div class="clients-container">
      <h2 class="clients-title">Liste des Clients</h2>

      <div class="d-flex justify-content-between align-items-center">
        <!-- Filtre de recherche -->
        <div class="search-box">
          <i class="fas fa-search"></i>
          <input type="text" placeholder="Rechercher un client..." [(ngModel)]="searchText">
        </div>

        <!-- <button (click)="openAddClientModal()" class="btn-add">
          <i class="fas fa-plus-circle"></i> Ajouter un client
        </button> -->
      </div>

      <div class="table-responsive">
        <table class="clients-table">
          <thead>
            <tr class="table-header">
              <th>Num</th>
              <th>Avatar</th>
              <th>Nom</th>
              <th>Email</th>
              <th>Date de creation</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr class="client-row" *ngFor="let client of filteredClients(); let i = index">
              <td>{{ i + 1 }}</td>
              <td>
                <img
                  [src]="client.avatar"
                  alt="avatar"
                  width="50"
                  height="50"
                  class="rounded-circle"
                  *ngIf="client.avatar"
                />
              </td>
              <td>{{ client.name }}</td>
              <td>{{ client.email }}</td>
              <td>{{ client.created_at | date: 'dd/MM/yyyy' }}</td>

              <td class="actions-cell">
                <!-- Edit Icon -->
                <i class="fas fa-edit text-primary icon-action" title="Edit" (click)="openEditClientModal(client)"></i>

                <!-- Delete Icon -->
                <i class="fas fa-trash-alt text-danger icon-action" title="Delete" (click)="deleteClient(client.id)"></i>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal -->
<div class="modal fade" id="clientModal" tabindex="-1" aria-labelledby="clientModalLabel" aria-hidden="true">
  <!-- Contenu du modal inchangé -->
</div>