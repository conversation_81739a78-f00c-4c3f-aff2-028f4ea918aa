import { Component, OnInit } from '@angular/core';
import { User } from 'src/models/User.model';
import { UserService } from 'src/services/user.service';
import * as bootstrap from 'bootstrap';


@Component({
  selector: 'app-user-list-admin',
  templateUrl: './user-list-admin.component.html',
  styleUrls: ['./user-list-admin.component.css']
})
export class UserListAdminComponent implements OnInit {
  clients: User[] = [];
  selectedClient: User = { id: 0, name: '', email: '', role: '', avatar: '', created_at: '', updated_at: '' };
  searchText: string = '';  // Variable pour la recherche

  constructor(private userService: UserService) {}

  ngOnInit(): void {
    this.loadClients();
  }

  // Charger la liste des clients
  loadClients(): void {
    this.userService.getClients().subscribe({
      next: (data) => {
        this.clients = data;
      },
      error: (err) => {
        console.error('Erreur lors du chargement des clients :', err);
      }
    });
  }

  // Méthode de filtrage des clients en fonction de la recherche
  filteredClients(): User[] {
    return this.clients.filter(client =>
      client.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
      client.email.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  // Ouvrir la modale pour ajouter un client
  openAddClientModal(): void {
    this.selectedClient = { id: 0, name: '', email: '', role: 'client', avatar: '', created_at: '', updated_at: '' };
    const modal = new bootstrap.Modal(document.getElementById('clientModal')!);
    modal.show();
  }

  // Ouvrir la modale pour modifier un client
  openEditClientModal(client: User): void {
    this.selectedClient = { ...client };
    const modal = new bootstrap.Modal(document.getElementById('clientModal')!);
    modal.show();
  }

  // Sauvegarder un client (ajouter ou modifier)
  saveClient(): void {
    if (this.selectedClient.id) {
      // Modification d'un client existant
      this.userService.updateClient(this.selectedClient, this.selectedClient.id).subscribe({
        next: () => {
          this.loadClients();
          this.closeModal();
        },
        error: (err) => {
          console.error('Erreur lors de la mise à jour du client :', err);
        }
      });
    } else {
      // Ajout d'un nouveau client
      this.userService.updateClient(this.selectedClient, 0).subscribe({
        next: () => {
          this.loadClients();
          this.closeModal();
        },
        error: (err) => {
          console.error('Erreur lors de l\'ajout du client :', err);
        }
      });
    }
  }

  // Supprimer un client
  deleteClient(id: number): void {
    if (confirm("Confirmer la suppression ?")) {
      this.userService.deleteClient(id).subscribe({
        next: () => {
          this.loadClients();
        },
        error: (err) => {
          console.error('Erreur lors de la suppression du client :', err);
        }
      });
    }
  }

  // Fermer la modale
  closeModal(): void {
    const modal = bootstrap.Modal.getInstance(document.getElementById('clientModal')!);
    modal?.hide();
  }
}


