.container {
	max-width: 100vw;
	margin: 0 auto;
	padding: 1.5rem 0;
}

main {
	margin-bottom: 6rem;
}

body {
	font-size: 18px;
}

h2 {
	font-weight: bold;
	font-size: 30px;
	margin-bottom: 0;
}

h3 {
	font-size: 30px;
}

i {
	margin-right: 8px;
}

hr {
	border-top: 1.5px solid #d4af37;
	width: 104px;
	margin: 0.5rem auto 1rem;
}

form {
	max-width: 100%;
}

/* ****************** */
.header {
	text-align: center;
	width: 50%;
	margin: 0 auto 4rem auto;
}

.header p {
	margin: 0 auto;
	max-width: 500px;
}

/* main div with div for form and contactus with background image */
.main {
	background: linear-gradient(rgba(0, 0, 0, 0.68), rgba(0, 0, 0, 0.68)),
		url('../img/Rectangle\ 75.webp');
	background-size: cover;
	background-repeat: no-repeat;
	height: 959px;
	display: flex;
	align-items: center;
	width: 100%;
}

/* Contact div contain form and contact us */
.contact {
	background: #ffffff;
	border-radius: 5px;
	display: flex;
	align-items: center;
	justify-content: space-around;
	max-width: 931px;
	height: 719px;
	margin: auto;
	padding: 0 1.5rem;
}

.contact-form {
	width: 53%;
}

.form-control {
	border: 1px solid #333333;
	box-sizing: border-box;
	border-radius: 4px;
	margin: 1rem 0;
	padding: 16px 20px;
}

.form-control::placeholder {
	font-family: 'Source Sans Pro', sans-serif;
}

.form-control:focus {
	border: 1px solid #d4af37;
}

#email,
#name {
	width: 45%;
	height: 58px;
}

#comment {
	height: 363px;
}

/* the input mail and name from form */
.contact-detail {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.contact-us {
	width: 37%;
	height: 320px;
	background: linear-gradient(rgba(0, 0, 0, 0.68), rgba(0, 0, 0, 0.68)),
		url('../img/Rectangle\ 57.webp');
	background-position: center top;
	color: white;
	display: flex;
	flex-direction: column;
	justify-content: space-around;
	padding: 2rem 0;
}

.contact-us > span,
.contact-us h3 {
	margin-left: 1.7rem;
}

.btn {
	font-size: 16px;
	padding: 0.8rem 1.8rem;
	background-color: #d4af37;
	color: white;
	border-radius: 5px;
	border: none;
	font-family: 'Source Sans Pro', sans-serif;
}

@media only screen and (max-width: 1000px) {
	.contact {
		width: 80%;
	}

	.contact-us > span {
		margin-left: 1rem;
		font-size: 16px;
	}

	.header {
		width: 60%;
	}
}
@media only screen and (max-width: 850px) {
	.contact {
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
		padding: 0 1.5rem;
		height: 90%;
	}

	.contact-form {
		width: 85%;
	}

	.contact-us {
		width: 85%;
		height: 25%;
		color: white;
		padding: 2rem 0;
	}

	.contact-detail {
		flex-direction: column;
	}

	#email,
	#name {
		width: 100%;
	}

	textarea {
		height: 60%;
	}
}
@media only screen and (max-width: 500px) {
	.header {
		width: 70%;
	}

	#comment {
		height: 250px;
	}

	.contact {
		width: 95%;
	}

	.contact-form {
		width: 95%;
	}

	.contact-us {
		width: 95%;
		height: 25%;
		color: white;
		padding: 2rem 0;
	}
}
