    :root {
      --primary: #2a3d66;
      --secondary: #d4b483;
      --accent: #5e8b7e;
      --light: #f8f5f2;
      --dark: #1a2a3a;
      --text: #333333;
      --text-light: #777777;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Montserrat', sans-serif;
      color: var(--text);
      background-color: var(--light);
      line-height: 1.6;
    }

    h1, h2, h3, h4 {
      font-family: 'Playfair Display', serif;
      font-weight: 600;
      color: var(--dark);
    }

    .btn {
      display: inline-block;
      padding: 12px 30px;
      background-color: var(--secondary);
      color: var(--dark);
      text-decoration: none;
      border-radius: 4px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }

    .btn:hover {
      background-color: var(--accent);
      color: white;
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    header {
      background-color: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      position: fixed;
      width: 100%;
      z-index: 1000;
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 24px;
      font-weight: 700;
      color: var(--primary);
    }

    .logo span {
      color: var(--secondary);
    }

    nav ul {
      display: flex;
      list-style: none;
    }

    nav ul li {
      margin-left: 30px;
    }

    nav ul li a {
      text-decoration: none;
      color: var(--dark);
      font-weight: 500;
      transition: color 0.3s;
    }

    nav ul li a:hover {
      color: var(--secondary);
    }

    /* Hero Section */
    .hero {
      height: 100vh;
      background: linear-gradient(rgba(42, 61, 102, 0.7), rgba(42, 61, 102, 0.7)),
                  url('https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
      background-size: cover;
      background-position: center;
      display: flex;
      align-items: center;
      color: white;
      padding-top: 80px;
    }

    .hero-content {
      max-width: 600px;
    }

    .hero h1 {
      font-size: 48px;
      margin-bottom: 20px;
      color: white;
    }

    .hero p {
      font-size: 18px;
      margin-bottom: 30px;
      opacity: 0.9;
    }

    /* Features Section */
    .features {
      padding: 100px 0;
      background-color: white;
    }

    .section-title {
      text-align: center;
      margin-bottom: 60px;
    }

    .section-title h2 {
      font-size: 36px;
      position: relative;
      display: inline-block;
      padding-bottom: 15px;
    }

    .section-title h2::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background-color: var(--secondary);
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
    }

    .feature-card {
      background-color: var(--light);
      padding: 40px 30px;
      border-radius: 8px;
      text-align: center;
      transition: transform 0.3s;
    }

    .feature-card:hover {
      transform: translateY(-10px);
    }

    .feature-icon {
      font-size: 40px;
      color: var(--secondary);
      margin-bottom: 20px;
    }

    .feature-card h3 {
      margin-bottom: 15px;
    }

    /* Rooms Section */
    .rooms {
      padding: 100px 0;
      background-color: var(--light);
    }

    .rooms-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
    }

    .room-card {
      background-color: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .room-img {
      height: 250px;
      overflow: hidden;
    }

    .room-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s;
    }

    .room-card:hover .room-img img {
      transform: scale(1.1);
    }

    .room-info {
      padding: 25px;
    }

    .room-info h3 {
      margin-bottom: 10px;
    }

    .room-price {
      color: var(--secondary);
      font-weight: 600;
      font-size: 20px;
      margin: 15px 0;
    }

    .room-features {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px;
    }

    .room-features span {
      margin-right: 15px;
      margin-bottom: 10px;
      font-size: 14px;
    }

    .room-features i {
      color: var(--secondary);
      margin-right: 5px;
    }

    /* Testimonials */
    .testimonials {
      padding: 100px 0;
      background-color: white;
    }

    .testimonials-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
    }

    .testimonial-card {
      background-color: var(--light);
      padding: 30px;
      border-radius: 8px;
      position: relative;
    }

    .testimonial-card::before {
      content: '"';
      position: absolute;
      top: 20px;
      left: 20px;
      font-size: 60px;
      font-family: 'Playfair Display', serif;
      color: rgba(212, 180, 131, 0.2);
    }

    .testimonial-content {
      position: relative;
      z-index: 1;
      margin-bottom: 20px;
    }

    .testimonial-author {
      display: flex;
      align-items: center;
    }

    .author-img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 15px;
    }

    .author-img img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .author-info h4 {
      margin-bottom: 5px;
    }

    .author-info p {
      color: var(--text-light);
      font-size: 14px;
    }

    /* Footer */
    footer {
      background-color: var(--dark);
      color: white;
      padding: 80px 0 30px;
    }

    .footer-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 40px;
      margin-bottom: 50px;
    }

    .footer-col h3 {
      color: white;
      margin-bottom: 25px;
      position: relative;
      padding-bottom: 10px;
    }

    .footer-col h3::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 50px;
      height: 2px;
      background-color: var(--secondary);
    }

    .footer-col p {
      margin-bottom: 20px;
      opacity: 0.8;
    }

    .footer-links li {
      margin-bottom: 10px;
      list-style: none;
    }

    .footer-links a {
      color: white;
      text-decoration: none;
      opacity: 0.8;
      transition: opacity 0.3s;
    }

    .footer-links a:hover {
      opacity: 1;
      color: var(--secondary);
    }

    .social-links {
      display: flex;
      margin-top: 20px;
    }

    .social-links a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background-color: rgba(255,255,255,0.1);
      border-radius: 50%;
      margin-right: 10px;
      color: white;
      transition: all 0.3s;
    }

    .social-links a:hover {
      background-color: var(--secondary);
      color: var(--dark);
    }

    .copyright {
      text-align: center;
      padding-top: 30px;
      border-top: 1px solid rgba(255,255,255,0.1);
      opacity: 0.7;
      font-size: 14px;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .nav-container {
        flex-direction: column;
      }

      nav ul {
        margin-top: 20px;
      }

      nav ul li {
        margin-left: 15px;
        margin-right: 15px;
      }

      .hero h1 {
        font-size: 36px;
      }

      .section-title h2 {
        font-size: 30px;
      }
    }

