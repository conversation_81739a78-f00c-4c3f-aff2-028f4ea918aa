export interface Reservation {
    id?: number;
    client_id: number;
    chambre_id: number;
    date_debut: string | Date; // Support des deux formats
    date_fin: string | Date;
    status?: 'en_attente' | 'confirmé' | 'annulé' | 'terminé';

    // Propriétés étendues pour la compatibilité backend
    nombre_personnes?: number;
    nombre_nuits?: number;
    prix_total?: number;
    prix_par_nuit?: number;
    notes?: string;
    demandes_speciales?: string;
    telephone?: string;
    email?: string;

    // Informations de facturation
    mode_paiement?: 'carte' | 'especes' | 'virement' | 'cheque';
    statut_paiement?: 'en_attente' | 'payé' | 'partiellement_payé' | 'remboursé';
    montant_acompte?: number;

    // Métadonnées
    created_at?: string;
    updated_at?: string;
    confirmed_at?: string;
    cancelled_at?: string;

    // Relations (pour l'affichage)
    client?: {
        id: number;
        name: string;
        email: string;
        telephone?: string;
    };
    chambre?: {
        id: number;
        type_chambre: string;
        prix_par_nuit: number;
        hotel: {
            id: number;
            nom: string;
        };
    };
}
