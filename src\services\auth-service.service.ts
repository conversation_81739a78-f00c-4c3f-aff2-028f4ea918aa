import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AuthServiceService {
  private baseUrl = 'http://localhost:8000/api/users';

  // ⬇️ Nouveau : observable pour suivre l'état de connexion
  private loggedIn = new BehaviorSubject<boolean>(this.hasToken());
  private userNameSubject = new BehaviorSubject<string | null>(localStorage.getItem('user_name'));
  private userRoleSubject = new BehaviorSubject<string | null>(localStorage.getItem('user_role')); // Suivi du rôle



  constructor(private http: HttpClient) { }

  login(email: string, password: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/login`, { email, password });
  }

  register(name: string, email: string, password: string, password_confirmation: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/register`, {
      name,
      email,
      password,
      password_confirmation,
      role: 'client'
    });
  }

  setToken(token: string): void {
    localStorage.setItem('auth_token', token);
    this.loggedIn.next(true); // ✅ mettre à jour le status connecté
  }

  setUserName(name: string): void {
    localStorage.setItem('user_name', name);
    this.userNameSubject.next(name);
  }

  setUserRole(role: string): void {
    localStorage.setItem('user_role', role); // Assurez-vous que le rôle est bien enregistré dans localStorage
    this.userRoleSubject.next(role); // Mettre à jour l'observable avec le rôle
  }
  

  getUserName(): string | null {
    return localStorage.getItem('user_name');
  }
  

  getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  getUserId(): string | null {
    return localStorage.getItem('user_id');
  }


  isAuthenticated(): boolean {
      // Vérifier si un token ou utilisateur est présent dans localStorage
    return !!localStorage.getItem('auth_token'); // Exemple
  }

  hasToken(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  logout(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_name');
    localStorage.removeItem('user_role');
    localStorage.removeItem('user_id');


    this.userNameSubject.next(null);
    this.userRoleSubject.next(null); // Réinitialiser le rôle
    this.loggedIn.next(false);
  }


  

  get isLoggedIn$(): Observable<boolean> {
    return this.loggedIn.asObservable();
  }

  get userName$(): Observable<string | null> {
    return this.userNameSubject.asObservable();
  }

  get userRole$(): Observable<string | null> {
    return this.userRoleSubject.asObservable();
  }



  
}
