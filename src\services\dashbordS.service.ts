
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { StatisticsData } from 'src/models/staticdashbord';

@Injectable({
  providedIn: 'root'
})
export class dashbordSservice {
 private apiUrl = 'http://127.0.0.1:8000/api/statistics';

  constructor(private http: HttpClient) {}

  getStatistics(): Observable<StatisticsData> {
    return this.http.get<StatisticsData>(this.apiUrl);
  }

}