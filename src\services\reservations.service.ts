import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Reservation } from 'src/models/Reservation.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReservationService {

  private apiUrl = 'http://localhost:8000/api/reservations';

  constructor(private http: HttpClient) {}

  createReservation(reservation: Reservation): Observable<any> {
    return this.http.post(this.apiUrl, reservation);
  }

  // createReservation(data: any): Observable<any> {
  //   console.log('Creating reservation with data:', data); // Debug logging
  //   return this.http.post(this.apiUrl, data);
  // }

  // checkRoomAvailability(chambreId: number, dateDebut: string, dateFin: string): Observable<any> {
  //   console.log('Checking availability:', { chambreId, dateDebut, dateFin }); // Debug logging
  //   return this.http.get(`${this.apiUrl}/check-availability`, {
  //     params: {
  //       chambre_id: chambreId.toString(),
  //       date_debut: dateDebut,
  //       date_fin: dateFin
  //     }
  //   });}
  
  getAllReservations(): Observable<any> {
    return this.http.get<any>(this.apiUrl);
  }

 
  getReservationsByUser(userId: number): Observable<Reservation[]> {
    return this.http.get<Reservation[]>(`${this.apiUrl}/user/${userId}`);
  }
  
  confirmReservation(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/confirmer`, {});
  }

  cancelReservation(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/annuler`, {});
  }

  deleteRes(id: number) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
}
