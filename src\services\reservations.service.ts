import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Reservation } from 'src/models/Reservation.model';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ReservationService {

  private apiUrl = 'http://localhost:8000/api/reservations';

  constructor(private http: HttpClient) {}

  createReservation(reservation: Reservation): Observable<any> {
    // Convertir les dates en format string si nécessaire
    const reservationData = {
      ...reservation,
      date_debut: this.formatDate(reservation.date_debut),
      date_fin: this.formatDate(reservation.date_fin)
    };

    console.log('Creating reservation with data:', reservationData);
    return this.http.post(this.apiUrl, reservationData);
  }

  // Vérifier la disponibilité d'une chambre
  checkRoomAvailability(chambreId: number, dateDebut: string, dateFin: string): Observable<any> {
    console.log('Checking availability:', { chambreId, dateDebut, dateFin });
    return this.http.get(`${this.apiUrl}/check-availability`, {
      params: {
        chambre_id: chambreId.toString(),
        date_debut: dateDebut,
        date_fin: dateFin
      }
    });
  }

  // Calculer le prix total d'une réservation
  calculateReservationPrice(chambreId: number, dateDebut: string, dateFin: string, nombrePersonnes?: number): Observable<any> {
    return this.http.get(`${this.apiUrl}/calculate-price`, {
      params: {
        chambre_id: chambreId.toString(),
        date_debut: dateDebut,
        date_fin: dateFin,
        nombre_personnes: nombrePersonnes?.toString() || '1'
      }
    });
  }

  getAllReservations(): Observable<any> {
    return this.http.get<any>(this.apiUrl);
  }


  getReservationsByUser(userId: number): Observable<Reservation[]> {
    return this.http.get<Reservation[]>(`${this.apiUrl}/user/${userId}`);
  }

  confirmReservation(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/confirmer`, {});
  }

  cancelReservation(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${id}/annuler`, {});
  }

  deleteRes(id: number) {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }

  // Mettre à jour une réservation
  updateReservation(id: number, reservation: Partial<Reservation>): Observable<any> {
    const reservationData = {
      ...reservation,
      date_debut: reservation.date_debut ? this.formatDate(reservation.date_debut) : undefined,
      date_fin: reservation.date_fin ? this.formatDate(reservation.date_fin) : undefined
    };
    return this.http.put(`${this.apiUrl}/${id}`, reservationData);
  }

  // Obtenir une réservation par ID
  getReservationById(id: number): Observable<Reservation> {
    return this.http.get<Reservation>(`${this.apiUrl}/${id}`);
  }

  // Obtenir les réservations par statut
  getReservationsByStatus(status: string): Observable<Reservation[]> {
    return this.http.get<Reservation[]>(`${this.apiUrl}/status/${status}`);
  }

  // Obtenir les réservations par hôtel
  getReservationsByHotel(hotelId: number): Observable<Reservation[]> {
    return this.http.get<Reservation[]>(`${this.apiUrl}/hotel/${hotelId}`);
  }

  // Obtenir les réservations par chambre
  getReservationsByRoom(chambreId: number): Observable<Reservation[]> {
    return this.http.get<Reservation[]>(`${this.apiUrl}/room/${chambreId}`);
  }

  // Obtenir les statistiques des réservations
  getReservationStatistics(): Observable<any> {
    return this.http.get(`${this.apiUrl}/statistics`);
  }

  // Envoyer un email de confirmation
  sendConfirmationEmail(reservationId: number): Observable<any> {
    return this.http.post(`${this.apiUrl}/${reservationId}/send-confirmation`, {});
  }

  // Fonction utilitaire pour formater les dates
  private formatDate(date: string | Date): string {
    if (date instanceof Date) {
      return date.toISOString().split('T')[0];
    }
    if (typeof date === 'string') {
      // Si c'est déjà au format YYYY-MM-DD, on le retourne tel quel
      if (date.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return date;
      }
      // Sinon, on essaie de le convertir
      return new Date(date).toISOString().split('T')[0];
    }
    return date;
  }

  // Calculer le nombre de nuits
  calculateNights(dateDebut: string | Date, dateFin: string | Date): number {
    const debut = new Date(dateDebut);
    const fin = new Date(dateFin);
    const diffTime = Math.abs(fin.getTime() - debut.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
