import { Injectable } from '@angular/core';

export interface StatusInfo {
  class: string;
  style: any;
  icon: string;
  text: string;
  color: string;
}

@Injectable({
  providedIn: 'root'
})
export class StatusService {

  constructor() { }

  /**
   * Obtenir toutes les informations d'un statut
   */
  getStatusInfo(status: string): StatusInfo {
    const normalizedStatus = status.toLowerCase();
    
    switch (normalizedStatus) {
      case 'confirmé':
      case 'confirmee':
      case 'confirmed':
        return {
          class: 'status-confirmed',
          style: { backgroundColor: '#28a745', color: 'white' },
          icon: 'fas fa-check-circle',
          text: 'Confirmé',
          color: '#28a745'
        };
        
      case 'en_attente':
      case 'en attente':
      case 'pending':
        return {
          class: 'status-pending',
          style: { backgroundColor: '#fd7e14', color: 'white' },
          icon: 'fas fa-clock',
          text: 'En attente',
          color: '#fd7e14'
        };
        
      case 'annulé':
      case 'annulee':
      case 'cancelled':
        return {
          class: 'status-cancelled',
          style: { backgroundColor: '#dc3545', color: 'white' },
          icon: 'fas fa-times-circle',
          text: 'Annulé',
          color: '#dc3545'
        };
        
      case 'terminé':
      case 'terminee':
      case 'completed':
        return {
          class: 'status-completed',
          style: { backgroundColor: '#6c757d', color: 'white' },
          icon: 'fas fa-flag-checkered',
          text: 'Terminé',
          color: '#6c757d'
        };
        
      default:
        return {
          class: 'status-unknown',
          style: { backgroundColor: '#6c757d', color: 'white' },
          icon: 'fas fa-question-circle',
          text: 'Inconnu',
          color: '#6c757d'
        };
    }
  }

  /**
   * Obtenir la classe CSS pour le statut
   */
  getStatusClass(status: string): string {
    return this.getStatusInfo(status).class;
  }

  /**
   * Obtenir le style inline pour le statut
   */
  getStatusStyle(status: string): any {
    return this.getStatusInfo(status).style;
  }

  /**
   * Obtenir l'icône pour le statut
   */
  getStatusIcon(status: string): string {
    return this.getStatusInfo(status).icon;
  }

  /**
   * Obtenir le texte formaté pour le statut
   */
  getStatusText(status: string): string {
    return this.getStatusInfo(status).text;
  }

  /**
   * Obtenir la couleur principale du statut
   */
  getStatusColor(status: string): string {
    return this.getStatusInfo(status).color;
  }

  /**
   * Vérifier si un statut est valide
   */
  isValidStatus(status: string): boolean {
    const validStatuses = [
      'confirmé', 'confirmee', 'confirmed',
      'en_attente', 'en attente', 'pending',
      'annulé', 'annulee', 'cancelled',
      'terminé', 'terminee', 'completed'
    ];
    return validStatuses.includes(status.toLowerCase());
  }

  /**
   * Obtenir tous les statuts disponibles
   */
  getAllStatuses(): Array<{value: string, label: string, color: string}> {
    return [
      { value: 'en_attente', label: 'En attente', color: '#fd7e14' },
      { value: 'confirmé', label: 'Confirmé', color: '#28a745' },
      { value: 'annulé', label: 'Annulé', color: '#dc3545' },
      { value: 'terminé', label: 'Terminé', color: '#6c757d' }
    ];
  }

  /**
   * Obtenir les statuts avec leurs statistiques
   */
  getStatusStats(reservations: any[]): Array<{status: string, count: number, percentage: number, color: string}> {
    const total = reservations.length;
    const stats = this.getAllStatuses().map(status => {
      const count = reservations.filter(r => 
        r.status.toLowerCase() === status.value.toLowerCase()
      ).length;
      const percentage = total > 0 ? Math.round((count / total) * 100) : 0;
      
      return {
        status: status.label,
        count,
        percentage,
        color: status.color
      };
    });

    return stats;
  }
}
