import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { User } from 'src/models/User.model';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class UserService {

  private apiUrl = 'http://localhost:8000/api/users';

  constructor(private http: HttpClient) {}

  // 🔍 Obtenir tous les utilisateurs
  getAllUsers(): Observable<User[]> {
    return this.http.get<any>(this.apiUrl).pipe(
      map(response => response.data)
    );
  }

  // 🔍 Obtenir uniquement les clients
  getClients(): Observable<User[]> {
    return this.http.get<any>(this.apiUrl).pipe(
      map(response => response.data.filter((user: User) => user.role === 'client'))
    );
  }

  updateClient(clientData: any, clientId: number): Observable<any> {
    return this.http.put(`${this.apiUrl}/${clientId}`, clientData);
  }
  

  deleteClient(clientId: number): Observable<any> {
    return this.http.delete<any>(`${this.apiUrl}/${clientId}`);
  }
}